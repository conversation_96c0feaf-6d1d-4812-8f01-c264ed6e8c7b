package axdr

import (
	"fmt"
	"strings"
)

// Type 返回 BYTE STRING 的 A-XDR 类型
func (bs *ByteString) Type() AXDRType {
	return TypeByteString
}

// String 返回 BYTE STRING 的字符串表示
func (bs *ByteString) String() string {
	var hex strings.Builder
	for i, b := range bs.Value {
		if i > 0 {
			hex.WriteString(" ")
		}
		hex.WriteString(fmt.Sprintf("%02X", b))
	}
	
	if bs.IsConstrained {
		return fmt.Sprintf("BYTE STRING(SIZE(%d)): %s", bs.Size, hex.String())
	}
	return fmt.Sprintf("BYTE STRING: %s", hex.String())
}

// Encode 编码 BYTE STRING 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.5 节实现
func (bs *ByteString) Encode() ([]byte, error) {
	if bs.IsConstrained {
		return bs.encodeConstrained()
	}
	return bs.encodeUnconstrained()
}

// encodeConstrained 编码规定大小的字节串（固定长度）
// 根据标准 6.5.1 节实现
func (bs *ByteString) encodeConstrained() ([]byte, error) {
	// 检查字节长度是否符合规定大小
	if len(bs.Value) != bs.Size {
		return nil, fmt.Errorf("%w: byte length %d does not match size %d", 
			ErrInvalidLength, len(bs.Value), bs.Size)
	}
	
	// 字节串的A-XDR编码只有内容域
	// 内容域中的字节数等于规定的大小
	// 字节串的字节从第一个起至最后一个，只需排在内容域的各字节中
	result := make([]byte, bs.Size)
	copy(result, bs.Value)
	
	return result, nil
}

// encodeUnconstrained 编码未规定大小的字节串（可变长度）
// 根据标准 6.5.2 节实现
func (bs *ByteString) encodeUnconstrained() ([]byte, error) {
	// 编码长度域
	lengthBytes, err := bs.encodeLengthField()
	if err != nil {
		return nil, err
	}
	
	// 内容域就是字节串本身
	contentBytes := bs.Value
	
	// 组合长度域和内容域
	result := make([]byte, len(lengthBytes)+len(contentBytes))
	copy(result, lengthBytes)
	copy(result[len(lengthBytes):], contentBytes)
	
	return result, nil
}

// encodeLengthField 编码长度域
// 长度域表示的值等于字节串的字节数
// 长度域本身的编码规则和可变长度的整数的编码相似
// 但因负值对于长度域没有意义，因此整数的编码为二进制编码，而不是2的补码表示的二进制数
func (bs *ByteString) encodeLengthField() ([]byte, error) {
	byteLength := int64(len(bs.Value))
	
	// 如果字节长度在 0-127 范围内，只用一个字节编码
	if byteLength >= 0 && byteLength <= 127 {
		return []byte{byte(byteLength)}, nil
	}
	
	// 否则使用长度域 + 内容域的格式
	// 计算内容域所需字节数
	contentLength := CalculateByteLength(byteLength)
	if contentLength > 127 {
		return nil, fmt.Errorf("%w: byte length too large", ErrOutOfRange)
	}
	
	// 长度域：第8位设为1，其他7位表示内容域字节数
	lengthByte := byte(0x80 | contentLength)
	
	// 内容域：无符号二进制编码
	contentBytes := make([]byte, contentLength)
	value := uint64(byteLength)
	
	switch contentLength {
	case 1:
		contentBytes[0] = byte(value)
	case 2:
		contentBytes[0] = byte(value >> 8)
		contentBytes[1] = byte(value)
	case 3:
		contentBytes[0] = byte(value >> 16)
		contentBytes[1] = byte(value >> 8)
		contentBytes[2] = byte(value)
	case 4:
		contentBytes[0] = byte(value >> 24)
		contentBytes[1] = byte(value >> 16)
		contentBytes[2] = byte(value >> 8)
		contentBytes[3] = byte(value)
	case 8:
		contentBytes[0] = byte(value >> 56)
		contentBytes[1] = byte(value >> 48)
		contentBytes[2] = byte(value >> 40)
		contentBytes[3] = byte(value >> 32)
		contentBytes[4] = byte(value >> 24)
		contentBytes[5] = byte(value >> 16)
		contentBytes[6] = byte(value >> 8)
		contentBytes[7] = byte(value)
	default:
		return nil, fmt.Errorf("%w: unsupported content length %d", ErrInvalidLength, contentLength)
	}
	
	// 组合长度域和内容域
	result := make([]byte, 1+contentLength)
	result[0] = lengthByte
	copy(result[1:], contentBytes)
	
	return result, nil
}

// Decode 从 A-XDR 格式解码 BYTE STRING
func (bs *ByteString) Decode(data []byte) (int, error) {
	if len(data) == 0 {
		return 0, ErrInvalidData
	}
	
	if bs.IsConstrained {
		return bs.decodeConstrained(data)
	}
	return bs.decodeUnconstrained(data)
}

// decodeConstrained 解码规定大小的字节串
func (bs *ByteString) decodeConstrained(data []byte) (int, error) {
	if len(data) < bs.Size {
		return 0, ErrBufferTooSmall
	}
	
	// 复制数据
	bs.Value = make([]byte, bs.Size)
	copy(bs.Value, data[:bs.Size])
	
	return bs.Size, nil
}

// decodeUnconstrained 解码未规定大小的字节串
func (bs *ByteString) decodeUnconstrained(data []byte) (int, error) {
	// 解码长度域
	byteLength, lengthBytes, err := bs.decodeLengthField(data)
	if err != nil {
		return 0, err
	}
	
	if len(data) < lengthBytes+byteLength {
		return 0, ErrBufferTooSmall
	}
	
	// 复制内容域数据
	bs.Value = make([]byte, byteLength)
	copy(bs.Value, data[lengthBytes:lengthBytes+byteLength])
	
	return lengthBytes + byteLength, nil
}

// decodeLengthField 解码长度域
func (bs *ByteString) decodeLengthField(data []byte) (int, int, error) {
	if len(data) == 0 {
		return 0, 0, ErrInvalidData
	}
	
	firstByte := data[0]
	
	// 如果第8位为0，表示单字节编码
	if firstByte&0x80 == 0 {
		return int(firstByte), 1, nil
	}
	
	// 否则是长度域 + 内容域格式
	contentLength := int(firstByte & 0x7F)
	if contentLength == 0 || contentLength > 8 {
		return 0, 0, fmt.Errorf("%w: invalid content length %d", ErrInvalidLength, contentLength)
	}
	
	if len(data) < 1+contentLength {
		return 0, 0, ErrBufferTooSmall
	}
	
	contentBytes := data[1 : 1+contentLength]
	
	// 解码内容域（无符号二进制）
	var value uint64
	for i, b := range contentBytes {
		value = (value << 8) | uint64(b)
		if i >= 7 { // 防止溢出
			break
		}
	}
	
	return int(value), 1 + contentLength, nil
}
