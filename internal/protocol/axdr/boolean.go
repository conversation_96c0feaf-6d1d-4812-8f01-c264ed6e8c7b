package axdr

import "fmt"

// Type 返回 BOOLEAN 的 A-XDR 类型
func (b *Boolean) Type() AXDRType {
	return TypeBoolean
}

// String 返回 BOOLEAN 的字符串表示
func (b *Boolean) String() string {
	return fmt.Sprintf("BOOLEAN: %t", b.Value)
}

// Encode 编码 BOOLEAN 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.2 节实现
// 布尔值的A-XDR编码只有内容域，由一个字节组成
// FALSE: 字节为零(所有位都是零)
// TRUE: 字节可以是任意非零值
func (b *Boolean) Encode() ([]byte, error) {
	result := make([]byte, 1)
	
	if b.Value {
		// TRUE: 可以是任意非零值，这里使用 0xFF
		result[0] = 0xFF
	} else {
		// FALSE: 所有位都是零
		result[0] = 0x00
	}
	
	return result, nil
}

// Decode 从 A-XDR 格式解码 BOOLEAN
func (b *Boolean) Decode(data []byte) (int, error) {
	if len(data) < 1 {
		return 0, ErrBufferTooSmall
	}
	
	// 根据标准，FALSE为零，TRUE为任意非零值
	b.Value = data[0] != 0x00
	
	return 1, nil
}
