package axdr

import (
	"fmt"
	"strings"
	"time"
)

// Codec A-XDR 编解码器
type Codec struct {
	// 可以添加配置选项
}

// NewCodec 创建新的 A-XDR 编解码器
func NewCodec() *Codec {
	return &Codec{}
}

// Encode 编码 A-XDR 值
func (c *Codec) Encode(value AXDRValue) ([]byte, error) {
	if value == nil {
		return nil, fmt.Errorf("%w: value is nil", ErrInvalidData)
	}

	return value.Encode()
}

// Decode 解码 A-XDR 值
func (c *Codec) Decode(data []byte, value AXDRValue) (int, error) {
	if value == nil {
		return 0, fmt.Errorf("%w: value is nil", ErrInvalidData)
	}

	return value.Decode(data)
}

// EncodeMultiple 编码多个 A-XDR 值
func (c *Codec) EncodeMultiple(values ...AXDRValue) ([]byte, error) {
	var result []byte

	for i, value := range values {
		if value == nil {
			return nil, fmt.Errorf("%w: value %d is nil", ErrInvalidData, i)
		}

		encoded, err := value.Encode()
		if err != nil {
			return nil, fmt.Errorf("failed to encode value %d: %w", i, err)
		}

		result = append(result, encoded...)
	}

	return result, nil
}

// DecodeMultiple 解码多个 A-XDR 值
func (c *Codec) DecodeMultiple(data []byte, values ...AXDRValue) (int, error) {
	offset := 0

	for i, value := range values {
		if value == nil {
			return 0, fmt.Errorf("%w: value %d is nil", ErrInvalidData, i)
		}

		consumed, err := value.Decode(data[offset:])
		if err != nil {
			return 0, fmt.Errorf("failed to decode value %d: %w", i, err)
		}

		offset += consumed
	}

	return offset, nil
}

// 工具函数

// FormatHex 格式化字节数组为十六进制字符串
func FormatHex(data []byte) string {
	var hex strings.Builder
	for i, b := range data {
		if i > 0 {
			hex.WriteString(" ")
		}
		hex.WriteString(fmt.Sprintf("%02X", b))
	}
	return hex.String()
}

// ParseHex 解析十六进制字符串为字节数组
func ParseHex(hexStr string) ([]byte, error) {
	// 移除空格
	hexStr = strings.ReplaceAll(hexStr, " ", "")

	if len(hexStr)%2 != 0 {
		return nil, fmt.Errorf("invalid hex string length")
	}

	result := make([]byte, len(hexStr)/2)
	for i := 0; i < len(hexStr); i += 2 {
		var b byte
		_, err := fmt.Sscanf(hexStr[i:i+2], "%02X", &b)
		if err != nil {
			return nil, fmt.Errorf("invalid hex character at position %d: %w", i, err)
		}
		result[i/2] = b
	}

	return result, nil
}

// ValidateAXDRType 验证 A-XDR 类型
func ValidateAXDRType(value AXDRValue) error {
	if value == nil {
		return fmt.Errorf("%w: value is nil", ErrInvalidData)
	}

	switch v := value.(type) {
	case *Integer:
		if v.IsConstrained {
			if v.Value < v.MinValue || v.Value > v.MaxValue {
				return fmt.Errorf("%w: integer value %d not in range [%d, %d]",
					ErrOutOfRange, v.Value, v.MinValue, v.MaxValue)
			}
		}
	case *BitString:
		if v.IsConstrained && v.BitLength != v.Size {
			return fmt.Errorf("%w: bit string length %d does not match size %d",
				ErrInvalidLength, v.BitLength, v.Size)
		}
	case *ByteString:
		if v.IsConstrained && len(v.Value) != v.Size {
			return fmt.Errorf("%w: byte string length %d does not match size %d",
				ErrInvalidLength, len(v.Value), v.Size)
		}
	case *SequenceOf:
		if v.IsConstrained && len(v.Elements) != v.Size {
			return fmt.Errorf("%w: sequence of length %d does not match size %d",
				ErrInvalidLength, len(v.Elements), v.Size)
		}
	}

	return nil
}

// CreateTypeFromTag 根据标记创建对应的 A-XDR 类型
func CreateTypeFromTag(tag AXDRType) AXDRValue {
	switch tag {
	case TypeInteger:
		return NewInteger(0)
	case TypeBoolean:
		return NewBoolean(false)
	case TypeEnumerated:
		return NewEnumerated(0)
	case TypeBitString:
		return NewBitString(nil, 0)
	case TypeByteString:
		return NewByteString(nil)
	case TypeChoice:
		return NewChoice(0, nil)
	case TypeSequence:
		return NewSequence(nil)
	case TypeSequenceOf:
		return NewSequenceOf(nil)
	case TypeVisibleString:
		return NewVisibleString("")
	case TypeGeneralizedTime:
		return NewGeneralizedTime(time.Time{})
	case TypeNull:
		return NewNull()
	default:
		return nil
	}
}

// GetTypeName 获取类型名称
func GetTypeName(axdrType AXDRType) string {
	switch axdrType {
	case TypeInteger:
		return "INTEGER"
	case TypeBoolean:
		return "BOOLEAN"
	case TypeEnumerated:
		return "ENUMERATED"
	case TypeBitString:
		return "BIT STRING"
	case TypeByteString:
		return "BYTE STRING"
	case TypeChoice:
		return "CHOICE"
	case TypeSequence:
		return "SEQUENCE"
	case TypeSequenceOf:
		return "SEQUENCE OF"
	case TypeVisibleString:
		return "VisibleString"
	case TypeGeneralizedTime:
		return "GeneralizedTime"
	case TypeNull:
		return "NULL"
	default:
		return "UNKNOWN"
	}
}

// CalculatePaddingBits 计算位串的填充位数
func CalculatePaddingBits(bitLength int) int {
	if bitLength%8 == 0 {
		return 0
	}
	return 8 - (bitLength % 8)
}

// AlignToByteLength 将位长度对齐到字节长度
func AlignToByteLength(bitLength int) int {
	return (bitLength + 7) / 8
}

// IsValidVisibleString 检查字符串是否为有效的可视字符串
func IsValidVisibleString(s string) bool {
	for _, r := range s {
		// 可视字符范围：0x20-0x7E
		if r < 0x20 || r > 0x7E {
			return false
		}
	}
	return true
}

// 预定义的常用类型

// CommonTypes 常用的 A-XDR 类型定义
var CommonTypes = struct {
	// 常用整数类型
	Int8   func(int8) *Integer
	Int16  func(int16) *Integer
	Int32  func(int32) *Integer
	UInt8  func(uint8) *Integer
	UInt16 func(uint16) *Integer
	UInt32 func(uint32) *Integer

	// 常用字符串类型
	String func(string) *VisibleString

	// 常用时间类型
	Time func(time.Time) *GeneralizedTime
}{
	Int8: func(v int8) *Integer {
		return NewConstrainedInteger(int64(v), -128, 127)
	},
	Int16: func(v int16) *Integer {
		return NewConstrainedInteger(int64(v), -32768, 32767)
	},
	Int32: func(v int32) *Integer {
		return NewConstrainedInteger(int64(v), -2147483648, 2147483647)
	},
	UInt8: func(v uint8) *Integer {
		return NewConstrainedInteger(int64(v), 0, 255)
	},
	UInt16: func(v uint16) *Integer {
		return NewConstrainedInteger(int64(v), 0, 65535)
	},
	UInt32: func(v uint32) *Integer {
		return NewConstrainedInteger(int64(v), 0, 4294967295)
	},
	String: func(v string) *VisibleString {
		return NewVisibleString(v)
	},
	Time: func(v time.Time) *GeneralizedTime {
		return NewGeneralizedTime(v)
	},
}
