# DL/T 698.45 Data 类型实现

本包实现了 DL/T 698.45 协议中的核心 Data CHOICE 类型，基于 A-XDR 编码标准。

## 概述

Data 类型是一个 CHOICE 类型，包含了 DL/T 698.45 协议中所有可能的数据类型。它支持 38 种不同的数据类型，从基础类型（如 NULL、bool、integer）到复杂的协议特定类型（如 OAD、ROAD、OMD 等）。

## 支持的数据类型

### 基础数据类型
- **NULL** [0] - 空值
- **bool** [3] - 布尔值
- **integer** [15] - 8位有符号整数
- **long** [16] - 16位有符号整数
- **unsigned** [17] - 8位无符号整数
- **long-unsigned** [18] - 16位无符号整数
- **double-long** [5] - 32位有符号整数
- **double-long-unsigned** [6] - 32位无符号整数
- **long64** [20] - 64位有符号整数
- **long64-unsigned** [21] - 64位无符号整数

### 字符串类型
- **visible-string** [10] - ASCII 字符串
- **UTF8-string** [12] - UTF-8 字符串

### 二进制数据类型
- **octet-string** [9] - 字节串
- **bit-string** [4] - 位串

### 其他基础类型
- **enum** [22] - 枚举类型
- **float32** [23] - 32位浮点数（暂未实现）
- **float64** [24] - 64位浮点数（暂未实现）

### 复合类型
- **array** [1] - 数组（暂未实现）
- **structure** [2] - 结构体（暂未实现）

### 时间类型
- **date_time** [25] - 日期时间（暂未实现）
- **date** [26] - 日期（暂未实现）
- **time** [27] - 时间（暂未实现）
- **date_time_s** [28] - 秒级日期时间（暂未实现）

### 协议特定类型
- **OI** [80] - 对象标识符
- **OAD** [81] - 对象属性描述符（暂未实现）
- **ROAD** [82] - 记录对象属性描述符（暂未实现）
- **OMD** [83] - 对象方法描述符（暂未实现）
- **TI** [84] - 类型标识（暂未实现）
- **TSA** [85] - 时间戳认证（暂未实现）
- **MAC** [86] - 消息认证码（暂未实现）
- **RN** [87] - 随机数（暂未实现）
- **Region** [88] - 区域（暂未实现）
- **Scaler_Unit** [89] - 标量单位（暂未实现）
- **RSD** [90] - 记录选择描述符（暂未实现）
- **CSD** [91] - 捕获选择描述符（暂未实现）
- **MS** [92] - 测量状态（暂未实现）
- **SID** [93] - 安全标识（暂未实现）
- **SID_MAC** [94] - 安全标识MAC（暂未实现）
- **COMDCB** [95] - 通信数据控制块（暂未实现）
- **RCSD** [96] - 记录捕获选择描述符（暂未实现）
- **VQDS** [97] - 值质量描述符（暂未实现）

## 使用示例

### 创建 Data 实例

```go
import "tp.service/internal/protocol/dlt69845"

// 创建不同类型的 Data 实例
nullData := dlt69845.NewDataNull()
boolData := dlt69845.NewDataBool(true)
intData := dlt69845.NewDataInteger(123)
stringData := dlt69845.NewDataVisibleString("Hello")
bytesData := dlt69845.NewDataOctetString([]byte{0x01, 0x02, 0x03})
```

### 类型检查

```go
data := dlt69845.NewDataBool(true)

if data.IsBool() {
    fmt.Println("这是一个布尔类型")
}

if data.IsInteger() {
    fmt.Println("这是一个整数类型")
} else {
    fmt.Println("这不是整数类型")
}
```

### 值获取

```go
// 获取布尔值
boolData := dlt69845.NewDataBool(true)
if value, err := boolData.GetBool(); err == nil {
    fmt.Printf("布尔值: %t\n", value)
}

// 获取整数值
intData := dlt69845.NewDataInteger(123)
if value, err := intData.GetInteger(); err == nil {
    fmt.Printf("整数值: %d\n", value)
}

// 获取字符串值
stringData := dlt69845.NewDataVisibleString("Hello")
if value, err := stringData.GetString(); err == nil {
    fmt.Printf("字符串值: %s\n", value)
}
```

### 编码和解码

```go
import "tp.service/internal/protocol/axdr"

codec := axdr.NewCodec()

// 编码
data := dlt69845.NewDataInteger(123)
encoded, err := codec.Encode(data)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("编码结果: %X\n", encoded)

// 解码
decoded := dlt69845.NewData()
consumed, err := codec.Decode(encoded, decoded)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("解码消耗字节: %d\n", consumed)
fmt.Printf("解码结果: %s\n", decoded.String())
```

## 命令行工具

提供了命令行演示工具 `cmd/demo_data/main.go`：

```bash
# 运行演示
go run cmd/demo_data/main.go demo

# 编码特定值
go run cmd/demo_data/main.go encode bool true
go run cmd/demo_data/main.go encode integer 123
go run cmd/demo_data/main.go encode visible-string "Hello"

# 解码十六进制数据
go run cmd/demo_data/main.go decode "03FF"
go run cmd/demo_data/main.go decode "0F7B"
```

## 编码格式

Data 类型使用 A-XDR CHOICE 编码格式：
- 第一个字节是类型标签（tag）
- 后续字节是对应类型的 A-XDR 编码

例如：
- `NULL`: `00`
- `bool true`: `03 FF`
- `bool false`: `03 00`
- `integer 123`: `0F 7B`
- `visible-string "Hello"`: `0A 05 48 65 6C 6C 6F`

## 测试

运行测试套件：

```bash
go test ./internal/protocol/dlt69845 -v
```

## 注意事项

1. **浮点数支持**: 目前 float32 和 float64 类型暂时使用整数类型代替，需要后续实现专门的浮点数编码。

2. **复杂类型**: array、structure 等复杂类型需要额外的实现。

3. **协议特定类型**: 大部分协议特定类型（OAD、ROAD 等）需要根据具体的 DL/T 698.45 协议规范来实现。

4. **时间类型**: 时间相关类型需要实现专门的时间编码格式。

5. **类型安全**: 使用类型检查方法确保类型安全，避免类型转换错误。

## 扩展

要添加新的数据类型支持：

1. 在 `datatype.go` 中添加新的类型常量
2. 在 `data.go` 中添加对应的构造函数
3. 在 `createTypeMap()` 中添加类型映射
4. 在 `GetTypeName()` 中添加类型名称映射
5. 添加相应的测试用例

## 依赖

- `tp.service/internal/protocol/axdr` - A-XDR 编码库
- Go 1.24.2 或更高版本
