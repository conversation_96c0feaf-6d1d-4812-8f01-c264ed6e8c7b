package axdr

import (
	"testing"
	"time"
)

// TestInteger 测试 INTEGER 类型编解码
func TestInteger(t *testing.T) {
	tests := []struct {
		name     string
		value    int64
		expected []byte
	}{
		{"Zero", 0, []byte{0x00}},
		{"Positive small", 123, []byte{0x7B}},
		{"Positive large", 128, []byte{0x82, 0x00, 0x80}},
		{"Negative", -1, []byte{0x81, 0xFF}},
		{"Negative large", -128, []byte{0x81, 0x80}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			integer := NewInteger(tt.value)
			encoded, err := codec.Encode(integer)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != len(tt.expected) {
				t.<PERSON>("Expected length %d, got %d", len(tt.expected), len(encoded))
			}

			// 测试解码
			decoded := NewInteger(0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}

// TestConstrainedInteger 测试受约束的 INTEGER
func TestConstrainedInteger(t *testing.T) {
	tests := []struct {
		name     string
		value    int64
		min      int64
		max      int64
		expected []byte
	}{
		{"UInt8", 255, 0, 255, []byte{0xFF}},
		{"UInt16", 65535, 0, 65535, []byte{0xFF, 0xFF}},
		{"Int8", -128, -128, 127, []byte{0x80}},
		{"Int16", -32768, -32768, 32767, []byte{0x80, 0x00}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			integer := NewConstrainedInteger(tt.value, tt.min, tt.max)
			encoded, err := codec.Encode(integer)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != len(tt.expected) {
				t.Errorf("Expected length %d, got %d", len(tt.expected), len(encoded))
			}

			// 测试解码
			decoded := NewConstrainedInteger(0, tt.min, tt.max)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}

// TestBoolean 测试 BOOLEAN 类型编解码
func TestBoolean(t *testing.T) {
	tests := []struct {
		name     string
		value    bool
		expected []byte
	}{
		{"False", false, []byte{0x00}},
		{"True", true, []byte{0xFF}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			boolean := NewBoolean(tt.value)
			encoded, err := codec.Encode(boolean)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != 1 {
				t.Errorf("Expected length 1, got %d", len(encoded))
			}

			// 测试解码
			decoded := NewBoolean(false)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != 1 {
				t.Errorf("Expected consumed 1, got %d", consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %t, got %t", tt.value, decoded.Value)
			}
		})
	}
}

// TestEnumerated 测试 ENUMERATED 类型编解码
func TestEnumerated(t *testing.T) {
	tests := []struct {
		name     string
		value    byte
		expected []byte
	}{
		{"Zero", 0, []byte{0x00}},
		{"Max", 255, []byte{0xFF}},
		{"Middle", 128, []byte{0x80}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			enumerated := NewEnumerated(tt.value)
			encoded, err := codec.Encode(enumerated)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != 1 {
				t.Errorf("Expected length 1, got %d", len(encoded))
			}

			if encoded[0] != tt.expected[0] {
				t.Errorf("Expected %02X, got %02X", tt.expected[0], encoded[0])
			}

			// 测试解码
			decoded := NewEnumerated(0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != 1 {
				t.Errorf("Expected consumed 1, got %d", consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}

// TestBitString 测试 BIT STRING 类型编解码
func TestBitString(t *testing.T) {
	tests := []struct {
		name      string
		value     []byte
		bitLength int
		expected  []byte
	}{
		{"Empty", []byte{}, 0, []byte{0x00}},
		{"Single bit", []byte{0x80}, 1, []byte{0x01, 0x80}},
		{"Full byte", []byte{0xFF}, 8, []byte{0x08, 0xFF}},
		{"Partial byte", []byte{0xE0}, 3, []byte{0x03, 0xE0}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			bitString := NewBitString(tt.value, tt.bitLength)
			encoded, err := codec.Encode(bitString)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := NewBitString(nil, 0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.BitLength != tt.bitLength {
				t.Errorf("Expected bit length %d, got %d", tt.bitLength, decoded.BitLength)
			}
		})
	}
}

// TestByteString 测试 BYTE STRING 类型编解码
func TestByteString(t *testing.T) {
	tests := []struct {
		name     string
		value    []byte
		expected []byte
	}{
		{"Empty", []byte{}, []byte{0x00}},
		{"Single byte", []byte{0x42}, []byte{0x01, 0x42}},
		{"Multiple bytes", []byte{0x41, 0x42, 0x43, 0x44}, []byte{0x04, 0x41, 0x42, 0x43, 0x44}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			byteString := NewByteString(tt.value)
			encoded, err := codec.Encode(byteString)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := NewByteString(nil)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if len(decoded.Value) != len(tt.value) {
				t.Errorf("Expected length %d, got %d", len(tt.value), len(decoded.Value))
			}

			for i, b := range tt.value {
				if decoded.Value[i] != b {
					t.Errorf("Expected byte[%d] %02X, got %02X", i, b, decoded.Value[i])
				}
			}
		})
	}
}

// TestVisibleString 测试 VisibleString 类型编解码
func TestVisibleString(t *testing.T) {
	tests := []struct {
		name  string
		value string
	}{
		{"Empty", ""},
		{"Simple", "Hello"},
		{"With spaces", "Hello World"},
		{"Numbers", "12345"},
		{"Special chars", "!@#$%^&*()"},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			visibleString := NewVisibleString(tt.value)
			encoded, err := codec.Encode(visibleString)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := NewVisibleString("")
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %q, got %q", tt.value, decoded.Value)
			}
		})
	}
}

// TestGeneralizedTime 测试 GeneralizedTime 类型编解码
func TestGeneralizedTime(t *testing.T) {
	testTime := time.Date(2023, 12, 25, 15, 30, 45, 0, time.UTC)
	
	codec := NewCodec()

	// 测试编码
	generalizedTime := NewGeneralizedTime(testTime)
	encoded, err := codec.Encode(generalizedTime)
	if err != nil {
		t.Fatalf("Encode failed: %v", err)
	}

	// 测试解码
	decoded := NewGeneralizedTime(time.Time{})
	consumed, err := codec.Decode(encoded, decoded)
	if err != nil {
		t.Fatalf("Decode failed: %v", err)
	}

	if consumed != len(encoded) {
		t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
	}

	if !decoded.Value.Equal(testTime) {
		t.Errorf("Expected time %v, got %v", testTime, decoded.Value)
	}
}

// TestNull 测试 NULL 类型编解码
func TestNull(t *testing.T) {
	codec := NewCodec()

	// 测试编码
	null := NewNull()
	encoded, err := codec.Encode(null)
	if err != nil {
		t.Fatalf("Encode failed: %v", err)
	}

	if len(encoded) != 0 {
		t.Errorf("Expected empty encoding, got %d bytes", len(encoded))
	}

	// 测试解码
	decoded := NewNull()
	consumed, err := codec.Decode(encoded, decoded)
	if err != nil {
		t.Fatalf("Decode failed: %v", err)
	}

	if consumed != 0 {
		t.Errorf("Expected consumed 0, got %d", consumed)
	}
}
