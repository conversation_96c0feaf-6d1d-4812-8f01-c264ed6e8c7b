package axdr

import (
	"fmt"
	"strings"
)

// Type 返回 BIT STRING 的 A-XDR 类型
func (bs *BitString) Type() AXDRType {
	return TypeBitString
}

// String 返回 BIT STRING 的字符串表示
func (bs *BitString) String() string {
	var bits strings.Builder
	for i := 0; i < bs.BitLength; i++ {
		byteIndex := i / 8
		bitIndex := 7 - (i % 8) // 从高位开始
		if byteIndex < len(bs.Value) {
			if bs.Value[byteIndex]&(1<<bitIndex) != 0 {
				bits.WriteByte('1')
			} else {
				bits.WriteByte('0')
			}
		} else {
			bits.WriteByte('0')
		}
	}
	
	if bs.IsConstrained {
		return fmt.Sprintf("BIT STRING(SIZE(%d)): %s", bs.Size, bits.String())
	}
	return fmt.Sprintf("BIT STRING: %s", bits.String())
}

// Encode 编码 BIT STRING 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.4 节实现
func (bs *BitString) Encode() ([]byte, error) {
	if bs.IsConstrained {
		return bs.encodeConstrained()
	}
	return bs.encodeUnconstrained()
}

// encodeConstrained 编码规定大小的位串（固定长度）
// 根据标准 6.4.1 节实现
func (bs *BitString) encodeConstrained() ([]byte, error) {
	// 检查位长度是否符合规定大小
	if bs.BitLength != bs.Size {
		return nil, fmt.Errorf("%w: bit length %d does not match size %d", 
			ErrInvalidLength, bs.BitLength, bs.Size)
	}
	
	// 计算所需字节数
	byteLength := (bs.Size + 7) / 8
	result := make([]byte, byteLength)
	
	// 复制位数据
	copy(result, bs.Value)
	
	// 如果最后一个字节有未使用的位，确保它们为0（追踪位）
	if bs.Size%8 != 0 {
		lastByteUsedBits := bs.Size % 8
		mask := byte(0xFF << (8 - lastByteUsedBits))
		if len(result) > 0 {
			result[len(result)-1] &= mask
		}
	}
	
	return result, nil
}

// encodeUnconstrained 编码未规定大小的位串（可变长度）
// 根据标准 6.4.2 节实现
func (bs *BitString) encodeUnconstrained() ([]byte, error) {
	// 编码长度域
	lengthBytes, err := bs.encodeLengthField()
	if err != nil {
		return nil, err
	}
	
	// 计算内容域所需字节数
	contentByteLength := (bs.BitLength + 7) / 8
	contentBytes := make([]byte, contentByteLength)
	
	// 复制位数据
	copy(contentBytes, bs.Value)
	
	// 如果最后一个字节有未使用的位，确保它们为0（追踪位）
	if bs.BitLength%8 != 0 {
		lastByteUsedBits := bs.BitLength % 8
		mask := byte(0xFF << (8 - lastByteUsedBits))
		if len(contentBytes) > 0 {
			contentBytes[len(contentBytes)-1] &= mask
		}
	}
	
	// 组合长度域和内容域
	result := make([]byte, len(lengthBytes)+len(contentBytes))
	copy(result, lengthBytes)
	copy(result[len(lengthBytes):], contentBytes)
	
	return result, nil
}

// encodeLengthField 编码长度域
// 长度域表示的值等于位串编码值的位的个数
// 长度域本身的编码规则和可变长度的整数的编码相似
// 但因负值对于长度域没有意义，因此整数的编码为二进制编码，而不是2的补码表示的二进制数
func (bs *BitString) encodeLengthField() ([]byte, error) {
	bitLength := int64(bs.BitLength)
	
	// 如果位长度在 0-127 范围内，只用一个字节编码
	if bitLength >= 0 && bitLength <= 127 {
		return []byte{byte(bitLength)}, nil
	}
	
	// 否则使用长度域 + 内容域的格式
	// 计算内容域所需字节数
	contentLength := CalculateByteLength(bitLength)
	if contentLength > 127 {
		return nil, fmt.Errorf("%w: bit length too large", ErrOutOfRange)
	}
	
	// 长度域：第8位设为1，其他7位表示内容域字节数
	lengthByte := byte(0x80 | contentLength)
	
	// 内容域：无符号二进制编码
	contentBytes := make([]byte, contentLength)
	value := uint64(bitLength)
	
	switch contentLength {
	case 1:
		contentBytes[0] = byte(value)
	case 2:
		contentBytes[0] = byte(value >> 8)
		contentBytes[1] = byte(value)
	case 3:
		contentBytes[0] = byte(value >> 16)
		contentBytes[1] = byte(value >> 8)
		contentBytes[2] = byte(value)
	case 4:
		contentBytes[0] = byte(value >> 24)
		contentBytes[1] = byte(value >> 16)
		contentBytes[2] = byte(value >> 8)
		contentBytes[3] = byte(value)
	case 8:
		contentBytes[0] = byte(value >> 56)
		contentBytes[1] = byte(value >> 48)
		contentBytes[2] = byte(value >> 40)
		contentBytes[3] = byte(value >> 32)
		contentBytes[4] = byte(value >> 24)
		contentBytes[5] = byte(value >> 16)
		contentBytes[6] = byte(value >> 8)
		contentBytes[7] = byte(value)
	default:
		return nil, fmt.Errorf("%w: unsupported content length %d", ErrInvalidLength, contentLength)
	}
	
	// 组合长度域和内容域
	result := make([]byte, 1+contentLength)
	result[0] = lengthByte
	copy(result[1:], contentBytes)
	
	return result, nil
}

// Decode 从 A-XDR 格式解码 BIT STRING
func (bs *BitString) Decode(data []byte) (int, error) {
	if len(data) == 0 {
		return 0, ErrInvalidData
	}
	
	if bs.IsConstrained {
		return bs.decodeConstrained(data)
	}
	return bs.decodeUnconstrained(data)
}

// decodeConstrained 解码规定大小的位串
func (bs *BitString) decodeConstrained(data []byte) (int, error) {
	// 计算所需字节数
	byteLength := (bs.Size + 7) / 8
	
	if len(data) < byteLength {
		return 0, ErrBufferTooSmall
	}
	
	// 复制数据
	bs.Value = make([]byte, byteLength)
	copy(bs.Value, data[:byteLength])
	bs.BitLength = bs.Size
	
	return byteLength, nil
}

// decodeUnconstrained 解码未规定大小的位串
func (bs *BitString) decodeUnconstrained(data []byte) (int, error) {
	// 解码长度域
	bitLength, lengthBytes, err := bs.decodeLengthField(data)
	if err != nil {
		return 0, err
	}
	
	bs.BitLength = bitLength
	
	// 计算内容域字节数
	contentByteLength := (bitLength + 7) / 8
	
	if len(data) < lengthBytes+contentByteLength {
		return 0, ErrBufferTooSmall
	}
	
	// 复制内容域数据
	bs.Value = make([]byte, contentByteLength)
	copy(bs.Value, data[lengthBytes:lengthBytes+contentByteLength])
	
	return lengthBytes + contentByteLength, nil
}

// decodeLengthField 解码长度域
func (bs *BitString) decodeLengthField(data []byte) (int, int, error) {
	if len(data) == 0 {
		return 0, 0, ErrInvalidData
	}
	
	firstByte := data[0]
	
	// 如果第8位为0，表示单字节编码
	if firstByte&0x80 == 0 {
		return int(firstByte), 1, nil
	}
	
	// 否则是长度域 + 内容域格式
	contentLength := int(firstByte & 0x7F)
	if contentLength == 0 || contentLength > 8 {
		return 0, 0, fmt.Errorf("%w: invalid content length %d", ErrInvalidLength, contentLength)
	}
	
	if len(data) < 1+contentLength {
		return 0, 0, ErrBufferTooSmall
	}
	
	contentBytes := data[1 : 1+contentLength]
	
	// 解码内容域（无符号二进制）
	var value uint64
	for i, b := range contentBytes {
		value = (value << 8) | uint64(b)
		if i >= 7 { // 防止溢出
			break
		}
	}
	
	return int(value), 1 + contentLength, nil
}
