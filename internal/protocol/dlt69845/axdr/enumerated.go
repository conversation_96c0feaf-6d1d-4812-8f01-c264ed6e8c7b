package axdr

import "fmt"

// Type 返回 ENUMERATED 的 A-XDR 类型
func (e *Enumerated) Type() AXDRType {
	return TypeEnumerated
}

// String 返回 ENUMERATED 的字符串表示
func (e *Enumerated) String() string {
	return fmt.Sprintf("ENUMERATED: %d", e.Value)
}

// Encode 编码 ENUMERATED 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.3 节实现
// 枚举型A-XDR编码的取值范围为0~255
// 一个枚举型的A-XDR编码及取值范围与0~255的整型相同
// 可作为一个固定长度、受约束的无符号型整数INTEGER (0-255)编码，由一个字节组成
func (e *Enumerated) Encode() ([]byte, error) {
	// 枚举值范围检查已在类型定义时保证（byte类型自然限制在0-255）
	return []byte{e.Value}, nil
}

// Decode 从 A-XDR 格式解码 ENUMERATED
func (e *Enumerated) Decode(data []byte) (int, error) {
	if len(data) < 1 {
		return 0, ErrBufferTooSmall
	}
	
	e.Value = data[0]
	return 1, nil
}
