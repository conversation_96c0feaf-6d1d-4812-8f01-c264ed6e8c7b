package axdr

import "fmt"

// Type 返回 CHOICE 的 A-XDR 类型
func (c *Choice) Type() AXDRType {
	return TypeChoice
}

// String 返回 CHOICE 的字符串表示
func (c *Choice) String() string {
	if c.Value != nil {
		return fmt.Sprintf("CHOICE[%d]: %s", c.Tag, c.Value.String())
	}
	return fmt.Sprintf("CHOICE[%d]: <nil>", c.Tag)
}

// Encode 编码 CHOICE 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.6 节实现
// A-XDR要求选择（CHOICE）的所有备选在ASN.1中定义为显式标记类型
// 编码标识形成了标识符域
func (c *Choice) Encode() ([]byte, error) {
	if c.Value == nil {
		return nil, fmt.Errorf("%w: choice value is nil", ErrInvalidData)
	}
	
	// 编码选择的值
	valueBytes, err := c.Value.Encode()
	if err != nil {
		return nil, fmt.Errorf("failed to encode choice value: %w", err)
	}
	
	// A-XDR中，CHOICE类型需要标识符域来表示选择的类型
	// 这里使用简化的标记编码：标记字节 + 值的编码
	result := make([]byte, 1+len(valueBytes))
	result[0] = c.Tag
	copy(result[1:], valueBytes)
	
	return result, nil
}

// Decode 从 A-XDR 格式解码 CHOICE
func (c *Choice) Decode(data []byte) (int, error) {
	if len(data) < 1 {
		return 0, ErrBufferTooSmall
	}
	
	// 读取标记
	c.Tag = data[0]
	
	// 这里需要根据标记来确定具体的类型并解码
	// 由于我们不知道具体的选择类型，这里返回错误
	// 在实际使用中，应该根据具体的CHOICE定义来实现
	return 0, fmt.Errorf("%w: choice decoding requires specific type information", ErrInvalidType)
}

// DecodeWithTypes 使用类型映射解码 CHOICE
func (c *Choice) DecodeWithTypes(data []byte, typeMap map[byte]func() AXDRValue) (int, error) {
	if len(data) < 1 {
		return 0, ErrBufferTooSmall
	}
	
	// 读取标记
	c.Tag = data[0]
	
	// 根据标记创建对应的类型
	createFunc, exists := typeMap[c.Tag]
	if !exists {
		return 0, fmt.Errorf("%w: unknown choice tag %d", ErrInvalidType, c.Tag)
	}
	
	c.Value = createFunc()
	
	// 解码值
	consumed, err := c.Value.Decode(data[1:])
	if err != nil {
		return 0, fmt.Errorf("failed to decode choice value: %w", err)
	}
	
	return 1 + consumed, nil
}
