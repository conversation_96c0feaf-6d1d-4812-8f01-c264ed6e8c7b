package axdr

import (
	"fmt"
	"strings"
	"time"
)

// ExampleBasicTypes 演示基础类型的使用
func ExampleBasicTypes() {
	fmt.Println("=== A-XDR 基础类型编解码示例 ===")
	fmt.Println()

	codec := NewCodec()

	// INTEGER 示例
	fmt.Println("1. INTEGER 类型:")
	integer := NewInteger(12345)
	integerBytes, _ := codec.Encode(integer)
	fmt.Printf("   值: %d\n", integer.Value)
	fmt.Printf("   编码: %s\n", FormatHex(integerBytes))

	decodedInteger := NewInteger(0)
	codec.Decode(integerBytes, decodedInteger)
	fmt.Printf("   解码: %d\n", decodedInteger.Value)
	fmt.Println()

	// 受约束的 INTEGER 示例
	fmt.Println("2. 受约束的 INTEGER 类型 (0-255):")
	constrainedInt := NewConstrainedInteger(200, 0, 255)
	constrainedBytes, _ := codec.Encode(constrainedInt)
	fmt.Printf("   值: %d (范围: 0-255)\n", constrainedInt.Value)
	fmt.Printf("   编码: %s\n", FormatHex(constrainedBytes))
	fmt.Println()

	// BOOLEAN 示例
	fmt.Println("3. BOOLEAN 类型:")
	boolTrue := NewBoolean(true)
	boolFalse := NewBoolean(false)
	trueBytes, _ := codec.Encode(boolTrue)
	falseBytes, _ := codec.Encode(boolFalse)
	fmt.Printf("   TRUE 编码: %s\n", FormatHex(trueBytes))
	fmt.Printf("   FALSE 编码: %s\n", FormatHex(falseBytes))
	fmt.Println()

	// ENUMERATED 示例
	fmt.Println("4. ENUMERATED 类型:")
	enumerated := NewEnumerated(42)
	enumBytes, _ := codec.Encode(enumerated)
	fmt.Printf("   值: %d\n", enumerated.Value)
	fmt.Printf("   编码: %s\n", FormatHex(enumBytes))
	fmt.Println()

	// BIT STRING 示例
	fmt.Println("5. BIT STRING 类型:")
	bitString := NewBitString([]byte{0xE0}, 3) // 111 (3位)
	bitBytes, _ := codec.Encode(bitString)
	fmt.Printf("   位串: %s (3位)\n", bitString.String())
	fmt.Printf("   编码: %s\n", FormatHex(bitBytes))
	fmt.Println()

	// BYTE STRING 示例
	fmt.Println("6. BYTE STRING 类型:")
	byteString := NewByteString([]byte{0x41, 0x42, 0x43, 0x44}) // "ABCD"
	byteStringBytes, _ := codec.Encode(byteString)
	fmt.Printf("   字节串: %s\n", byteString.String())
	fmt.Printf("   编码: %s\n", FormatHex(byteStringBytes))
	fmt.Println()

	// VisibleString 示例
	fmt.Println("7. VisibleString 类型:")
	visibleString := NewVisibleString("Hello")
	visibleBytes, _ := codec.Encode(visibleString)
	fmt.Printf("   字符串: %s\n", visibleString.String())
	fmt.Printf("   编码: %s\n", FormatHex(visibleBytes))
	fmt.Println()

	// GeneralizedTime 示例
	fmt.Println("8. GeneralizedTime 类型:")
	now := time.Now().UTC()
	generalizedTime := NewGeneralizedTime(now)
	timeBytes, _ := codec.Encode(generalizedTime)
	fmt.Printf("   时间: %s\n", generalizedTime.String())
	fmt.Printf("   编码: %s\n", FormatHex(timeBytes))
	fmt.Println()

	// NULL 示例
	fmt.Println("9. NULL 类型:")
	null := NewNull()
	nullBytes, _ := codec.Encode(null)
	fmt.Printf("   NULL 编码: %s (空)\n", FormatHex(nullBytes))
	fmt.Println()
}

// ExampleSequence 演示 SEQUENCE 类型的使用
func ExampleSequence() {
	fmt.Println("=== SEQUENCE 类型示例 ===")
	fmt.Println()

	codec := NewCodec()

	// 创建一个简单的序列
	sequence := NewSequence([]SequenceElement{
		{Value: NewInteger(100)},
		{Value: NewBoolean(true)},
		{Value: NewVisibleString("Test")},
	})

	fmt.Printf("序列内容: %s\n", sequence.String())

	// 编码序列
	sequenceBytes, err := codec.Encode(sequence)
	if err != nil {
		fmt.Printf("编码失败: %v\n", err)
		return
	}
	fmt.Printf("编码结果: %s\n", FormatHex(sequenceBytes))
	fmt.Println()

	// 创建带可选项的序列
	fmt.Println("带可选项的序列:")
	sequenceWithOptional := NewSequence([]SequenceElement{
		{Value: NewInteger(200)},
		{Optional: NewOptional(true, NewVisibleString("Optional"))},
		{Optional: NewOptional(false, NewInteger(0))}, // 不存在的可选项
	})

	fmt.Printf("序列内容: %s\n", sequenceWithOptional.String())

	optionalBytes, err := codec.Encode(sequenceWithOptional)
	if err != nil {
		fmt.Printf("编码失败: %v\n", err)
		return
	}
	fmt.Printf("编码结果: %s\n", FormatHex(optionalBytes))
	fmt.Println()
}

// ExampleDLT69845Types 演示 DL/T 698.45 中常用的类型
func ExampleDLT69845Types() {
	fmt.Println("=== DL/T 698.45 常用类型示例 ===")
	fmt.Println()

	codec := NewCodec()

	// 使用预定义的常用类型
	fmt.Println("1. 常用整数类型:")

	int8Val := CommonTypes.Int8(-100)
	int8Bytes, _ := codec.Encode(int8Val)
	fmt.Printf("   Int8(-100): %s\n", FormatHex(int8Bytes))

	uint16Val := CommonTypes.UInt16(65000)
	uint16Bytes, _ := codec.Encode(uint16Val)
	fmt.Printf("   UInt16(65000): %s\n", FormatHex(uint16Bytes))

	uint32Val := CommonTypes.UInt32(4000000000)
	uint32Bytes, _ := codec.Encode(uint32Val)
	fmt.Printf("   UInt32(4000000000): %s\n", FormatHex(uint32Bytes))
	fmt.Println()

	// 时间类型
	fmt.Println("2. 时间类型:")
	timeVal := CommonTypes.Time(time.Date(2023, 12, 25, 15, 30, 45, 0, time.UTC))
	timeBytes, _ := codec.Encode(timeVal)
	fmt.Printf("   GeneralizedTime: %s\n", FormatHex(timeBytes))
	fmt.Println()

	// 字符串类型
	fmt.Println("3. 字符串类型:")
	stringVal := CommonTypes.String("DL/T 698.45")
	stringBytes, _ := codec.Encode(stringVal)
	fmt.Printf("   VisibleString: %s\n", FormatHex(stringBytes))
	fmt.Println()
}

// ExampleComplexStructure 演示复杂数据结构
func ExampleComplexStructure() {
	fmt.Println("=== 复杂数据结构示例 ===")
	fmt.Println()

	codec := NewCodec()

	// 模拟 DL/T 698.45 中的一个数据结构
	// 例如：设备信息结构
	deviceInfo := NewSequence([]SequenceElement{
		{Value: CommonTypes.UInt16(0x1234)},                               // 设备类型
		{Value: CommonTypes.String("Device001")},                          // 设备标识
		{Value: NewByteString([]byte{0x01, 0x02, 0x03, 0x04})},            // 设备地址
		{Optional: NewOptional(true, CommonTypes.String("Manufacturer"))}, // 可选的制造商信息
		{Value: CommonTypes.Time(time.Now().UTC())},                       // 时间戳
	})

	fmt.Printf("设备信息结构: %s\n", deviceInfo.String())

	// 编码
	deviceBytes, err := codec.Encode(deviceInfo)
	if err != nil {
		fmt.Printf("编码失败: %v\n", err)
		return
	}
	fmt.Printf("编码结果 (%d 字节): %s\n", len(deviceBytes), FormatHex(deviceBytes))
	fmt.Println()

	// 创建一个 SEQUENCE OF 示例
	fmt.Println("SEQUENCE OF 示例 (多个设备):")
	devices := NewSequenceOf([]AXDRValue{
		CommonTypes.String("Device001"),
		CommonTypes.String("Device002"),
		CommonTypes.String("Device003"),
	})

	fmt.Printf("设备列表: %s\n", devices.String())

	devicesBytes, err := codec.Encode(devices)
	if err != nil {
		fmt.Printf("编码失败: %v\n", err)
		return
	}
	fmt.Printf("编码结果: %s\n", FormatHex(devicesBytes))
	fmt.Println()
}

// ExampleErrorHandling 演示错误处理
func ExampleErrorHandling() {
	fmt.Println("=== 错误处理示例 ===")
	fmt.Println()

	codec := NewCodec()

	// 1. 约束违反
	fmt.Println("1. 约束违反:")
	invalidInt := NewConstrainedInteger(300, 0, 255) // 值超出范围
	err := ValidateAXDRType(invalidInt)
	if err != nil {
		fmt.Printf("   验证失败: %v\n", err)
	}
	fmt.Println()

	// 2. 无效数据解码
	fmt.Println("2. 无效数据解码:")
	invalidData := []byte{0xFF, 0xFF, 0xFF} // 无效的长度域
	integer := NewInteger(0)
	_, err = codec.Decode(invalidData, integer)
	if err != nil {
		fmt.Printf("   解码失败: %v\n", err)
	}
	fmt.Println()

	// 3. 缓冲区太小
	fmt.Println("3. 缓冲区太小:")
	smallBuffer := []byte{0x82} // 长度域指示需要2字节，但只有1字节
	_, err = codec.Decode(smallBuffer, integer)
	if err != nil {
		fmt.Printf("   解码失败: %v\n", err)
	}
	fmt.Println()
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	ExampleBasicTypes()
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	ExampleSequence()
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	ExampleDLT69845Types()
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	ExampleComplexStructure()
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	ExampleErrorHandling()
}
