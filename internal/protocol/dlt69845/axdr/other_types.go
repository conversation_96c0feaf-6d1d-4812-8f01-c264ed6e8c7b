package axdr

import (
	"fmt"
	"strings"
	"time"
)

// SequenceOf 实现

// Type 返回 SEQUENCE OF 的 A-XDR 类型
func (so *SequenceOf) Type() AXDRType {
	return TypeSequenceOf
}

// String 返回 SEQUENCE OF 的字符串表示
func (so *SequenceOf) String() string {
	var elements []string
	for i, elem := range so.Elements {
		if elem != nil {
			elements = append(elements, fmt.Sprintf("[%d]: %s", i, elem.String()))
		} else {
			elements = append(elements, fmt.Sprintf("[%d]: <nil>", i))
		}
	}
	
	if so.IsConstrained {
		return fmt.Sprintf("SEQUENCE OF(SIZE(%d)) { %s }", so.Size, strings.Join(elements, ", "))
	}
	return fmt.Sprintf("SEQUENCE OF { %s }", strings.Join(elements, ", "))
}

// Encode 编码 SEQUENCE OF 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.10 节实现
func (so *SequenceOf) Encode() ([]byte, error) {
	if so.IsConstrained {
		return so.encodeConstrained()
	}
	return so.encodeUnconstrained()
}

// encodeConstrained 编码规定大小的 SEQUENCE OF
func (so *SequenceOf) encodeConstrained() ([]byte, error) {
	if len(so.Elements) != so.Size {
		return nil, fmt.Errorf("%w: element count %d does not match size %d", 
			ErrInvalidLength, len(so.Elements), so.Size)
	}
	
	var result []byte
	for i, elem := range so.Elements {
		if elem == nil {
			return nil, fmt.Errorf("%w: sequence element %d is nil", ErrInvalidData, i)
		}
		
		elemBytes, err := elem.Encode()
		if err != nil {
			return nil, fmt.Errorf("failed to encode sequence element %d: %w", i, err)
		}
		result = append(result, elemBytes...)
	}
	
	return result, nil
}

// encodeUnconstrained 编码未规定大小的 SEQUENCE OF
func (so *SequenceOf) encodeUnconstrained() ([]byte, error) {
	// 编码长度域
	lengthBytes, err := so.encodeLengthField()
	if err != nil {
		return nil, err
	}
	
	// 编码所有元素
	var contentBytes []byte
	for i, elem := range so.Elements {
		if elem == nil {
			return nil, fmt.Errorf("%w: sequence element %d is nil", ErrInvalidData, i)
		}
		
		elemBytes, err := elem.Encode()
		if err != nil {
			return nil, fmt.Errorf("failed to encode sequence element %d: %w", i, err)
		}
		contentBytes = append(contentBytes, elemBytes...)
	}
	
	// 组合长度域和内容域
	result := make([]byte, len(lengthBytes)+len(contentBytes))
	copy(result, lengthBytes)
	copy(result[len(lengthBytes):], contentBytes)
	
	return result, nil
}

// encodeLengthField 编码 SEQUENCE OF 的长度域
func (so *SequenceOf) encodeLengthField() ([]byte, error) {
	elementCount := int64(len(so.Elements))
	
	// 如果元素数量在 0-127 范围内，只用一个字节编码
	if elementCount >= 0 && elementCount <= 127 {
		return []byte{byte(elementCount)}, nil
	}
	
	// 否则使用长度域 + 内容域的格式
	contentLength := CalculateByteLength(elementCount)
	if contentLength > 127 {
		return nil, fmt.Errorf("%w: element count too large", ErrOutOfRange)
	}
	
	// 长度域：第8位设为1，其他7位表示内容域字节数
	lengthByte := byte(0x80 | contentLength)
	
	// 内容域：无符号二进制编码
	contentBytes := make([]byte, contentLength)
	value := uint64(elementCount)
	
	for i := contentLength - 1; i >= 0; i-- {
		contentBytes[i] = byte(value)
		value >>= 8
	}
	
	// 组合长度域和内容域
	result := make([]byte, 1+contentLength)
	result[0] = lengthByte
	copy(result[1:], contentBytes)
	
	return result, nil
}

// Decode 从 A-XDR 格式解码 SEQUENCE OF
func (so *SequenceOf) Decode(data []byte) (int, error) {
	if so.IsConstrained {
		return so.decodeConstrained(data)
	}
	return so.decodeUnconstrained(data)
}

// decodeConstrained 解码规定大小的 SEQUENCE OF
func (so *SequenceOf) decodeConstrained(data []byte) (int, error) {
	// 这里需要知道每个元素的类型才能正确解码
	// 在实际使用中，应该提供元素类型信息
	return 0, fmt.Errorf("%w: constrained sequence of decoding requires element type information", ErrInvalidType)
}

// decodeUnconstrained 解码未规定大小的 SEQUENCE OF
func (so *SequenceOf) decodeUnconstrained(data []byte) (int, error) {
	// 解码长度域
	elementCount, lengthBytes, err := so.decodeLengthField(data)
	if err != nil {
		return 0, err
	}
	
	// 这里需要知道每个元素的类型才能正确解码
	// 在实际使用中，应该提供元素类型信息
	so.Elements = make([]AXDRValue, elementCount)
	
	return lengthBytes, fmt.Errorf("%w: sequence of decoding requires element type information", ErrInvalidType)
}

// decodeLengthField 解码 SEQUENCE OF 的长度域
func (so *SequenceOf) decodeLengthField(data []byte) (int, int, error) {
	if len(data) == 0 {
		return 0, 0, ErrInvalidData
	}
	
	firstByte := data[0]
	
	// 如果第8位为0，表示单字节编码
	if firstByte&0x80 == 0 {
		return int(firstByte), 1, nil
	}
	
	// 否则是长度域 + 内容域格式
	contentLength := int(firstByte & 0x7F)
	if contentLength == 0 || contentLength > 8 {
		return 0, 0, fmt.Errorf("%w: invalid content length %d", ErrInvalidLength, contentLength)
	}
	
	if len(data) < 1+contentLength {
		return 0, 0, ErrBufferTooSmall
	}
	
	contentBytes := data[1 : 1+contentLength]
	
	// 解码内容域（无符号二进制）
	var value uint64
	for _, b := range contentBytes {
		value = (value << 8) | uint64(b)
	}
	
	return int(value), 1 + contentLength, nil
}

// VisibleString 实现

// Type 返回 VisibleString 的 A-XDR 类型
func (vs *VisibleString) Type() AXDRType {
	return TypeVisibleString
}

// String 返回 VisibleString 的字符串表示
func (vs *VisibleString) String() string {
	return fmt.Sprintf("VisibleString: \"%s\"", vs.Value)
}

// Encode 编码 VisibleString 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.11 节实现
func (vs *VisibleString) Encode() ([]byte, error) {
	// VisibleString 编码类似于 BYTE STRING
	byteString := NewByteString([]byte(vs.Value))
	return byteString.Encode()
}

// Decode 从 A-XDR 格式解码 VisibleString
func (vs *VisibleString) Decode(data []byte) (int, error) {
	byteString := NewByteString(nil)
	consumed, err := byteString.Decode(data)
	if err != nil {
		return 0, err
	}
	
	vs.Value = string(byteString.Value)
	return consumed, nil
}

// GeneralizedTime 实现

// Type 返回 GeneralizedTime 的 A-XDR 类型
func (gt *GeneralizedTime) Type() AXDRType {
	return TypeGeneralizedTime
}

// String 返回 GeneralizedTime 的字符串表示
func (gt *GeneralizedTime) String() string {
	return fmt.Sprintf("GeneralizedTime: %s", gt.Value.Format("20060102150405Z"))
}

// Encode 编码 GeneralizedTime 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.12 节实现
func (gt *GeneralizedTime) Encode() ([]byte, error) {
	// GeneralizedTime 编码为 VisibleString 格式
	timeStr := gt.Value.UTC().Format("20060102150405Z")
	visibleString := NewVisibleString(timeStr)
	return visibleString.Encode()
}

// Decode 从 A-XDR 格式解码 GeneralizedTime
func (gt *GeneralizedTime) Decode(data []byte) (int, error) {
	visibleString := NewVisibleString("")
	consumed, err := visibleString.Decode(data)
	if err != nil {
		return 0, err
	}
	
	// 解析时间字符串
	parsedTime, err := time.Parse("20060102150405Z", visibleString.Value)
	if err != nil {
		return 0, fmt.Errorf("failed to parse GeneralizedTime: %w", err)
	}
	
	gt.Value = parsedTime
	return consumed, nil
}

// Null 实现

// Type 返回 NULL 的 A-XDR 类型
func (n *Null) Type() AXDRType {
	return TypeNull
}

// String 返回 NULL 的字符串表示
func (n *Null) String() string {
	return "NULL"
}

// Encode 编码 NULL 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.13 节实现
func (n *Null) Encode() ([]byte, error) {
	// NULL 值编码为空的内容域
	return []byte{}, nil
}

// Decode 从 A-XDR 格式解码 NULL
func (n *Null) Decode(data []byte) (int, error) {
	// NULL 值不消耗任何字节
	return 0, nil
}
