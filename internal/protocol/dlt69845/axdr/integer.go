package axdr

import (
	"encoding/binary"
	"fmt"
)

// Type 返回 INTEGER 的 A-XDR 类型
func (i *Integer) Type() AXDRType {
	return TypeInteger
}

// String 返回 INTEGER 的字符串表示
func (i *Integer) String() string {
	if i.IsConstrained {
		return fmt.Sprintf("INTEGER(%d..%d): %d", i.MinValue, i.MaxValue, i.Value)
	}
	return fmt.Sprintf("INTEGER: %d", i.Value)
}

// Encode 编码 INTEGER 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.1 节实现
func (i *Integer) Encode() ([]byte, error) {
	if i.IsConstrained {
		return i.encodeConstrained()
	}
	return i.encodeUnconstrained()
}

// encodeConstrained 编码受约束的整数（固定长度）
// 根据标准 6.1.1 节实现
func (i *Integer) encodeConstrained() ([]byte, error) {
	// 检查值是否在约束范围内
	if i.Value < i.MinValue || i.Value > i.MaxValue {
		return nil, fmt.Errorf("%w: value %d not in range [%d, %d]", 
			ErrOutOfRange, i.Value, i.MinValue, i.MaxValue)
	}

	result := make([]byte, i.ByteLength)
	
	if i.MinValue >= 0 {
		// 无符号整数编码 (6.1.1.1)
		return i.encodeUnsigned(result)
	} else {
		// 有符号整数编码 (6.1.1.2)
		return i.encodeSigned(result)
	}
}

// encodeUnsigned 编码无符号整数
func (i *Integer) encodeUnsigned(result []byte) ([]byte, error) {
	value := uint64(i.Value)
	
	switch len(result) {
	case 1:
		result[0] = byte(value)
	case 2:
		binary.BigEndian.PutUint16(result, uint16(value))
	case 3:
		// 3字节大端序编码
		result[0] = byte(value >> 16)
		result[1] = byte(value >> 8)
		result[2] = byte(value)
	case 4:
		binary.BigEndian.PutUint32(result, uint32(value))
	case 8:
		binary.BigEndian.PutUint64(result, value)
	default:
		return nil, fmt.Errorf("%w: unsupported byte length %d", ErrInvalidLength, len(result))
	}
	
	return result, nil
}

// encodeSigned 编码有符号整数（2的补码）
func (i *Integer) encodeSigned(result []byte) ([]byte, error) {
	value := i.Value
	
	switch len(result) {
	case 1:
		result[0] = byte(value)
	case 2:
		binary.BigEndian.PutUint16(result, uint16(value))
	case 3:
		// 3字节大端序编码（2的补码）
		result[0] = byte(value >> 16)
		result[1] = byte(value >> 8)
		result[2] = byte(value)
	case 4:
		binary.BigEndian.PutUint32(result, uint32(value))
	case 8:
		binary.BigEndian.PutUint64(result, uint64(value))
	default:
		return nil, fmt.Errorf("%w: unsupported byte length %d", ErrInvalidLength, len(result))
	}
	
	return result, nil
}

// encodeUnconstrained 编码未受约束的整数（可变长度）
// 根据标准 6.1.2 节实现
func (i *Integer) encodeUnconstrained() ([]byte, error) {
	value := i.Value
	
	// 如果值在 0-127 范围内，只用一个字节编码
	if value >= 0 && value <= 127 {
		return []byte{byte(value)}, nil
	}
	
	// 否则使用长度域 + 内容域的格式
	// 计算内容域所需字节数
	contentLength := CalculateByteLength(value)
	if contentLength > 127 {
		return nil, fmt.Errorf("%w: value too large", ErrOutOfRange)
	}
	
	// 长度域：第8位设为1，其他7位表示内容域字节数
	lengthByte := byte(0x80 | contentLength)
	
	// 内容域：2的补码表示
	contentBytes := make([]byte, contentLength)
	switch contentLength {
	case 1:
		contentBytes[0] = byte(value)
	case 2:
		binary.BigEndian.PutUint16(contentBytes, uint16(value))
	case 3:
		contentBytes[0] = byte(value >> 16)
		contentBytes[1] = byte(value >> 8)
		contentBytes[2] = byte(value)
	case 4:
		binary.BigEndian.PutUint32(contentBytes, uint32(value))
	case 8:
		binary.BigEndian.PutUint64(contentBytes, uint64(value))
	default:
		return nil, fmt.Errorf("%w: unsupported content length %d", ErrInvalidLength, contentLength)
	}
	
	// 组合长度域和内容域
	result := make([]byte, 1+contentLength)
	result[0] = lengthByte
	copy(result[1:], contentBytes)
	
	return result, nil
}

// Decode 从 A-XDR 格式解码 INTEGER
func (i *Integer) Decode(data []byte) (int, error) {
	if len(data) == 0 {
		return 0, ErrInvalidData
	}
	
	if i.IsConstrained {
		return i.decodeConstrained(data)
	}
	return i.decodeUnconstrained(data)
}

// decodeConstrained 解码受约束的整数
func (i *Integer) decodeConstrained(data []byte) (int, error) {
	if len(data) < i.ByteLength {
		return 0, ErrBufferTooSmall
	}
	
	contentBytes := data[:i.ByteLength]
	
	if i.MinValue >= 0 {
		// 无符号整数解码
		return i.decodeUnsigned(contentBytes)
	} else {
		// 有符号整数解码
		return i.decodeSigned(contentBytes)
	}
}

// decodeUnsigned 解码无符号整数
func (i *Integer) decodeUnsigned(data []byte) (int, error) {
	var value uint64
	
	switch len(data) {
	case 1:
		value = uint64(data[0])
	case 2:
		value = uint64(binary.BigEndian.Uint16(data))
	case 3:
		value = uint64(data[0])<<16 | uint64(data[1])<<8 | uint64(data[2])
	case 4:
		value = uint64(binary.BigEndian.Uint32(data))
	case 8:
		value = binary.BigEndian.Uint64(data)
	default:
		return 0, fmt.Errorf("%w: unsupported byte length %d", ErrInvalidLength, len(data))
	}
	
	i.Value = int64(value)
	
	// 检查值是否在约束范围内
	if i.Value < i.MinValue || i.Value > i.MaxValue {
		return 0, fmt.Errorf("%w: decoded value %d not in range [%d, %d]", 
			ErrOutOfRange, i.Value, i.MinValue, i.MaxValue)
	}
	
	return len(data), nil
}

// decodeSigned 解码有符号整数（2的补码）
func (i *Integer) decodeSigned(data []byte) (int, error) {
	var value int64
	
	switch len(data) {
	case 1:
		value = int64(int8(data[0]))
	case 2:
		value = int64(int16(binary.BigEndian.Uint16(data)))
	case 3:
		// 3字节有符号数解码
		temp := uint32(data[0])<<16 | uint32(data[1])<<8 | uint32(data[2])
		// 符号扩展
		if data[0]&0x80 != 0 {
			temp |= 0xFF000000
		}
		value = int64(int32(temp))
	case 4:
		value = int64(int32(binary.BigEndian.Uint32(data)))
	case 8:
		value = int64(binary.BigEndian.Uint64(data))
	default:
		return 0, fmt.Errorf("%w: unsupported byte length %d", ErrInvalidLength, len(data))
	}
	
	i.Value = value
	
	// 检查值是否在约束范围内
	if i.Value < i.MinValue || i.Value > i.MaxValue {
		return 0, fmt.Errorf("%w: decoded value %d not in range [%d, %d]", 
			ErrOutOfRange, i.Value, i.MinValue, i.MaxValue)
	}
	
	return len(data), nil
}

// decodeUnconstrained 解码未受约束的整数
func (i *Integer) decodeUnconstrained(data []byte) (int, error) {
	if len(data) == 0 {
		return 0, ErrInvalidData
	}
	
	firstByte := data[0]
	
	// 如果第8位为0，表示单字节编码
	if firstByte&0x80 == 0 {
		i.Value = int64(firstByte)
		return 1, nil
	}
	
	// 否则是长度域 + 内容域格式
	contentLength := int(firstByte & 0x7F)
	if contentLength == 0 || contentLength > 8 {
		return 0, fmt.Errorf("%w: invalid content length %d", ErrInvalidLength, contentLength)
	}
	
	if len(data) < 1+contentLength {
		return 0, ErrBufferTooSmall
	}
	
	contentBytes := data[1 : 1+contentLength]
	
	// 解码内容域（2的补码）
	var value int64
	switch contentLength {
	case 1:
		value = int64(int8(contentBytes[0]))
	case 2:
		value = int64(int16(binary.BigEndian.Uint16(contentBytes)))
	case 3:
		temp := uint32(contentBytes[0])<<16 | uint32(contentBytes[1])<<8 | uint32(contentBytes[2])
		if contentBytes[0]&0x80 != 0 {
			temp |= 0xFF000000
		}
		value = int64(int32(temp))
	case 4:
		value = int64(int32(binary.BigEndian.Uint32(contentBytes)))
	case 8:
		value = int64(binary.BigEndian.Uint64(contentBytes))
	default:
		return 0, fmt.Errorf("%w: unsupported content length %d", ErrInvalidLength, contentLength)
	}
	
	i.Value = value
	return 1 + contentLength, nil
}
