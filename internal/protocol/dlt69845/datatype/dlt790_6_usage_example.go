package datatype

import (
	"fmt"
	"strings"
)

// DLT790_6_Usage provides usage examples for the extracted DL/T 790.6-2010 standard content
type DLT790_6_Usage struct{}

// NewDLT790_6_Usage creates a new usage example instance
func NewDLT790_6_Usage() *DLT790_6_Usage {
	return &DLT790_6_Usage{}
}

// ShowBasicUsage demonstrates basic usage of the extracted content
func (u *DLT790_6_Usage) ShowBasicUsage() {
	fmt.Println("=== DL/T 790.6-2010 标准内容使用示例 ===")
	fmt.Println()

	// 获取完整内容
	content := GetDLT790_6_Content()
	fmt.Printf("标准文档总字符数: %d\n", len(content))
	fmt.Println()

	// 获取章节信息
	if section, exists := GetDLT790_6_Section("full_content"); exists {
		fmt.Printf("完整内容可用: %t\n", len(section) > 0)
	}

	if extractedAt, exists := GetDLT790_6_Section("extracted_at"); exists {
		fmt.Printf("提取时间: %s\n", extractedAt)
	}

	if sourceFile, exists := GetDLT790_6_Section("source_file"); exists {
		fmt.Printf("源文件: %s\n", sourceFile)
	}
	fmt.Println()
}

// SearchContent searches for specific terms in the content
func (u *DLT790_6_Usage) SearchContent(searchTerm string) []string {
	content := GetDLT790_6_Content()
	lines := strings.Split(content, "\n")
	var results []string

	for i, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(searchTerm)) {
			results = append(results, fmt.Sprintf("第%d行: %s", i+1, strings.TrimSpace(line)))
		}
	}

	return results
}

// FindAXDRRules finds A-XDR encoding rules in the content
func (u *DLT790_6_Usage) FindAXDRRules() []string {
	keywords := []string{
		"A-XDR",
		"编码规则",
		"INTEGER",
		"BOOLEAN",
		"ENUMERATED",
		"BIT STRING",
		"BYTE STRING",
		"SEQUENCE",
		"CHOICE",
	}

	var allResults []string
	for _, keyword := range keywords {
		results := u.SearchContent(keyword)
		if len(results) > 0 {
			allResults = append(allResults, fmt.Sprintf("=== %s ===", keyword))
			allResults = append(allResults, results...)
			allResults = append(allResults, "")
		}
	}

	return allResults
}

// GetPageContent extracts content from a specific page
func (u *DLT790_6_Usage) GetPageContent(pageNum int) string {
	content := GetDLT790_6_Content()
	pageMarker := fmt.Sprintf("// ===== 第 %d 页 =====", pageNum)
	nextPageMarker := fmt.Sprintf("// ===== 第 %d 页 =====", pageNum+1)

	startIndex := strings.Index(content, pageMarker)
	if startIndex == -1 {
		return ""
	}

	endIndex := strings.Index(content, nextPageMarker)
	if endIndex == -1 {
		return content[startIndex:]
	}

	return content[startIndex:endIndex]
}

// GetEncodingExamples extracts encoding examples from the content
func (u *DLT790_6_Usage) GetEncodingExamples() []string {
	content := GetDLT790_6_Content()
	lines := strings.Split(content, "\n")
	var examples []string

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "编码") && (strings.Contains(line, "例") || strings.Contains(line, "示例")) {
			// 获取示例及其上下文
			start := i - 2
			if start < 0 {
				start = 0
			}
			end := i + 5
			if end >= len(lines) {
				end = len(lines) - 1
			}

			example := strings.Join(lines[start:end+1], "\n")
			examples = append(examples, example)
		}
	}

	return examples
}

// PrintStatistics prints statistics about the extracted content
func (u *DLT790_6_Usage) PrintStatistics() {
	content := GetDLT790_6_Content()
	lines := strings.Split(content, "\n")

	fmt.Println("=== DL/T 790.6-2010 标准内容统计 ===")
	fmt.Printf("总字符数: %d\n", len(content))
	fmt.Printf("总行数: %d\n", len(lines))

	// 统计页数
	pageCount := 0
	for _, line := range lines {
		if strings.Contains(line, "// ===== 第") && strings.Contains(line, "页 =====") {
			pageCount++
		}
	}
	fmt.Printf("总页数: %d\n", pageCount)

	// 统计关键词出现次数
	keywords := map[string]int{
		"A-XDR":       0,
		"编码":          0,
		"INTEGER":     0,
		"BOOLEAN":     0,
		"SEQUENCE":    0,
		"字节":          0,
		"位":           0,
	}

	contentLower := strings.ToLower(content)
	for keyword := range keywords {
		keywords[keyword] = strings.Count(contentLower, strings.ToLower(keyword))
	}

	fmt.Println("\n关键词统计:")
	for keyword, count := range keywords {
		fmt.Printf("  %s: %d 次\n", keyword, count)
	}
	fmt.Println()
}

// ExampleUsage demonstrates how to use the extracted content
func ExampleUsage() {
	usage := NewDLT790_6_Usage()

	// 显示基本使用方法
	usage.ShowBasicUsage()

	// 打印统计信息
	usage.PrintStatistics()

	// 搜索特定内容
	fmt.Println("=== 搜索 'A-XDR' 相关内容 ===")
	results := usage.SearchContent("A-XDR")
	for i, result := range results {
		if i < 5 { // 只显示前5个结果
			fmt.Println(result)
		}
	}
	if len(results) > 5 {
		fmt.Printf("... 还有 %d 个结果\n", len(results)-5)
	}
	fmt.Println()

	// 获取第一页内容
	fmt.Println("=== 第1页内容 ===")
	page1 := usage.GetPageContent(1)
	if len(page1) > 200 {
		fmt.Printf("%s...\n", page1[:200])
	} else {
		fmt.Println(page1)
	}
	fmt.Println()

	// 查找编码规则
	fmt.Println("=== A-XDR 编码规则 ===")
	rules := usage.FindAXDRRules()
	for i, rule := range rules {
		if i < 10 { // 只显示前10个结果
			fmt.Println(rule)
		}
	}
	if len(rules) > 10 {
		fmt.Printf("... 还有 %d 个结果\n", len(rules)-10)
	}
}
