package datatype

import (
	"strings"
	"testing"
)

func TestDLT790_6_Content(t *testing.T) {
	content := GetDLT790_6_Content()

	if len(content) == 0 {
		t.Fatal("DLT790_6_Content should not be empty")
	}

	t.Logf("Content length: %d characters", len(content))
}

func TestDLT790_6_Sections(t *testing.T) {
	// 测试获取完整内容
	fullContent, exists := GetDLT790_6_Section("full_content")
	if !exists {
		t.<PERSON>rror("full_content section should exist")
	}

	if len(fullContent) == 0 {
		t.Error("full_content should not be empty")
	}

	// 测试获取提取时间
	extractedAt, exists := GetDLT790_6_Section("extracted_at")
	if !exists {
		t.<PERSON>rror("extracted_at section should exist")
	}

	if len(extractedAt) == 0 {
		t.Error("extracted_at should not be empty")
	}

	t.Logf("Extracted at: %s", extractedAt)

	// 测试获取源文件
	sourceFile, exists := GetDLT790_6_Section("source_file")
	if !exists {
		t.Error("source_file section should exist")
	}

	if len(sourceFile) == 0 {
		t.<PERSON><PERSON><PERSON>("source_file should not be empty")
	}

	t.Logf("Source file: %s", sourceFile)
}

func TestDLT790_6_ContentStructure(t *testing.T) {
	content := GetDLT790_6_Content()

	// 检查是否包含页面标记
	if !strings.Contains(content, "// ===== 第 1 页 =====") {
		t.Error("Content should contain page markers")
	}

	// 检查是否包含标准相关内容
	expectedKeywords := []string{
		"DL/T 790.6",
		"A-XDR",
		"编码规则",
		"配电自动化",
	}

	for _, keyword := range expectedKeywords {
		if !strings.Contains(content, keyword) {
			t.Errorf("Content should contain keyword: %s", keyword)
		}
	}
}

func TestDLT790_6_Usage(t *testing.T) {
	usage := NewDLT790_6_Usage()

	// 测试搜索功能
	results := usage.SearchContent("A-XDR")
	if len(results) == 0 {
		t.Error("Should find A-XDR related content")
	}

	t.Logf("Found %d A-XDR related lines", len(results))

	// 测试获取页面内容
	page1 := usage.GetPageContent(1)
	if len(page1) == 0 {
		t.Error("Should be able to get page 1 content")
	}

	// 测试查找编码规则
	rules := usage.FindAXDRRules()
	if len(rules) == 0 {
		t.Error("Should find A-XDR encoding rules")
	}

	t.Logf("Found %d encoding rule entries", len(rules))
}

func TestDLT790_6_EncodingTypes(t *testing.T) {
	content := GetDLT790_6_Content()

	// 检查是否包含各种编码类型
	encodingTypes := []string{
		"INTEGER",
		"BOOLEAN",
		"ENUMERATED",
		"BIT STRING",
		"BYTE STRING",
		"SEQUENCE",
		"CHOICE",
	}

	for _, encodingType := range encodingTypes {
		if !strings.Contains(content, encodingType) {
			t.Errorf("Content should contain encoding type: %s", encodingType)
		}
	}
}

func TestDLT790_6_ChineseContent(t *testing.T) {
	content := GetDLT790_6_Content()

	// 检查是否包含中文内容
	chineseKeywords := []string{
		"编码",
		"规则",
		"标准",
		"配电",
		"自动化",
		"载波",
		"字节",
		"位串",
	}

	for _, keyword := range chineseKeywords {
		if !strings.Contains(content, keyword) {
			t.Errorf("Content should contain Chinese keyword: %s", keyword)
		}
	}
}

func BenchmarkDLT790_6_ContentAccess(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = GetDLT790_6_Content()
	}
}

func BenchmarkDLT790_6_SectionAccess(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, _ = GetDLT790_6_Section("full_content")
	}
}

func BenchmarkDLT790_6_Search(b *testing.B) {
	usage := NewDLT790_6_Usage()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = usage.SearchContent("A-XDR")
	}
}

// Example_getDLT790_6_Content demonstrates how to get the full content
func Example_getDLT790_6_Content() {
	content := GetDLT790_6_Content()

	// Print first 100 characters
	if len(content) > 100 {
		println(content[:100] + "...")
	} else {
		println(content)
	}
}

// Example_getDLT790_6_Section demonstrates how to get specific sections
func Example_getDLT790_6_Section() {
	// Get extraction time
	extractedAt, exists := GetDLT790_6_Section("extracted_at")
	if exists {
		println("Extracted at:", extractedAt)
	}

	// Get source file
	sourceFile, exists := GetDLT790_6_Section("source_file")
	if exists {
		println("Source file:", sourceFile)
	}
}

// Example_dLT790_6_Usage_SearchContent demonstrates content search
func Example_dLT790_6_Usage_SearchContent() {
	usage := NewDLT790_6_Usage()

	// Search for A-XDR related content
	results := usage.SearchContent("A-XDR")

	println("Found", len(results), "A-XDR related lines")

	// Print first few results
	for i, result := range results {
		if i >= 3 {
			break
		}
		println(result)
	}
}
