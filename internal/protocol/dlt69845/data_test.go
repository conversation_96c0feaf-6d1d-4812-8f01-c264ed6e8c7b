package dlt69845

import (
	"testing"

	"tp.service/internal/protocol/axdr"
)

func TestDataCreation(t *testing.T) {
	tests := []struct {
		name     string
		creator  func() *Data
		expected byte
	}{
		{"NULL", NewDataNull, DataTypeNull},
		{"Bool true", func() *Data { return NewDataBool(true) }, DataTypeBool},
		{"Bool false", func() *Data { return NewDataBool(false) }, DataTypeBool},
		{"Integer", func() *Data { return NewDataInteger(123) }, DataTypeInteger},
		{"Long", func() *Data { return NewDataLong(12345) }, DataTypeLong},
		{"Unsigned", func() *Data { return NewDataUnsigned(255) }, DataTypeUnsigned},
		{"LongUnsigned", func() *Data { return NewDataLongUnsigned(65535) }, DataTypeLongUnsigned},
		{"DoubleLong", func() *Data { return NewDataDoubleLong(-123456) }, DataTypeDoubleLong},
		{"DoubleLongUnsigned", func() *Data { return NewDataDoubleLongUnsigned(123456) }, DataTypeDoubleLongUnsigned},
		{"Long64", func() *Data { return NewDataLong64(-9223372036854775808) }, DataTypeLong64},
		{"Long64Unsigned", func() *Data { return NewDataLong64Unsigned(9223372036854775807) }, DataTypeLong64Unsigned},
		{"VisibleString", func() *Data { return NewDataVisibleString("Hello") }, DataTypeVisibleString},
		{"UTF8String", func() *Data { return NewDataUTF8String("世界") }, DataTypeUTF8String},
		{"OctetString", func() *Data { return NewDataOctetString([]byte{1, 2, 3}) }, DataTypeOctetString},
		{"BitString", func() *Data { return NewDataBitString([]bool{true, false, true}) }, DataTypeBitString},
		{"Enum", func() *Data { return NewDataEnum(42) }, DataTypeEnum},
		{"OI", func() *Data { return NewDataOI(0x4000) }, DataTypeOI},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data := tt.creator()
			if data.Tag != tt.expected {
				t.Errorf("Expected tag %d, got %d", tt.expected, data.Tag)
			}
			if data.Value == nil {
				t.Error("Value should not be nil")
			}
		})
	}
}

func TestDataTypeChecking(t *testing.T) {
	// 测试 NULL 类型
	nullData := NewDataNull()
	if !nullData.IsNull() {
		t.Error("NULL data should return true for IsNull()")
	}
	if nullData.IsBool() || nullData.IsInteger() || nullData.IsString() {
		t.Error("NULL data should return false for other type checks")
	}

	// 测试 bool 类型
	boolData := NewDataBool(true)
	if !boolData.IsBool() {
		t.Error("Bool data should return true for IsBool()")
	}
	if boolData.IsNull() || boolData.IsInteger() || boolData.IsString() {
		t.Error("Bool data should return false for other type checks")
	}

	// 测试整数类型
	integerTypes := []*Data{
		NewDataInteger(1),
		NewDataLong(2),
		NewDataUnsigned(3),
		NewDataLongUnsigned(4),
		NewDataDoubleLong(5),
		NewDataDoubleLongUnsigned(6),
		NewDataLong64(7),
		NewDataLong64Unsigned(8),
	}

	for i, data := range integerTypes {
		if !data.IsInteger() {
			t.Errorf("Integer type %d should return true for IsInteger()", i)
		}
		if data.IsNull() || data.IsBool() || data.IsString() {
			t.Errorf("Integer type %d should return false for other type checks", i)
		}
	}

	// 测试字符串类型
	stringTypes := []*Data{
		NewDataVisibleString("test"),
		NewDataUTF8String("测试"),
	}

	for i, data := range stringTypes {
		if !data.IsString() {
			t.Errorf("String type %d should return true for IsString()", i)
		}
		if data.IsNull() || data.IsBool() || data.IsInteger() {
			t.Errorf("String type %d should return false for other type checks", i)
		}
	}
}

func TestDataValueRetrieval(t *testing.T) {
	// 测试 bool 值获取
	boolData := NewDataBool(true)
	if value, err := boolData.GetBool(); err != nil {
		t.Errorf("GetBool() failed: %v", err)
	} else if !value {
		t.Error("Expected true, got false")
	}

	// 测试整数值获取
	intData := NewDataInteger(123)
	if value, err := intData.GetInteger(); err != nil {
		t.Errorf("GetInteger() failed: %v", err)
	} else if value != 123 {
		t.Errorf("Expected 123, got %d", value)
	}

	// 测试字符串值获取
	strData := NewDataVisibleString("Hello")
	if value, err := strData.GetString(); err != nil {
		t.Errorf("GetString() failed: %v", err)
	} else if value != "Hello" {
		t.Errorf("Expected 'Hello', got '%s'", value)
	}

	// 测试字节串值获取
	octetData := NewDataOctetString([]byte{1, 2, 3})
	if value, err := octetData.GetOctetString(); err != nil {
		t.Errorf("GetOctetString() failed: %v", err)
	} else if len(value) != 3 || value[0] != 1 || value[1] != 2 || value[2] != 3 {
		t.Errorf("Expected [1, 2, 3], got %v", value)
	}

	// 测试位串值获取
	bitData := NewDataBitString([]bool{true, false, true})
	if value, err := bitData.GetBitString(); err != nil {
		t.Errorf("GetBitString() failed: %v", err)
	} else if len(value) != 3 || !value[0] || value[1] || !value[2] {
		t.Errorf("Expected [true, false, true], got %v", value)
	}
}

func TestDataValueRetrievalErrors(t *testing.T) {
	// 测试类型不匹配的错误
	intData := NewDataInteger(123)

	// 尝试从整数获取 bool 值
	if _, err := intData.GetBool(); err == nil {
		t.Error("Expected error when getting bool from integer data")
	}

	// 尝试从整数获取字符串值
	if _, err := intData.GetString(); err == nil {
		t.Error("Expected error when getting string from integer data")
	}

	// 尝试从整数获取字节串值
	if _, err := intData.GetOctetString(); err == nil {
		t.Error("Expected error when getting octet string from integer data")
	}

	// 尝试从整数获取位串值
	if _, err := intData.GetBitString(); err == nil {
		t.Error("Expected error when getting bit string from integer data")
	}
}

func TestDataEncoding(t *testing.T) {
	codec := axdr.NewCodec()

	tests := []struct {
		name string
		data *Data
	}{
		{"NULL", NewDataNull()},
		{"Bool true", NewDataBool(true)},
		{"Bool false", NewDataBool(false)},
		{"Integer", NewDataInteger(123)},
		{"Long", NewDataLong(-12345)},
		{"Unsigned", NewDataUnsigned(255)},
		{"VisibleString", NewDataVisibleString("Test")},
		{"OctetString", NewDataOctetString([]byte{0x01, 0x02, 0x03})},
		{"BitString", NewDataBitString([]bool{true, false, true, false})},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			encoded, err := codec.Encode(tt.data)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) == 0 {
				t.Error("Encoded data should not be empty")
			}

			// 测试解码
			decoded := NewData()
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Tag != tt.data.Tag {
				t.Errorf("Expected tag %d, got %d", tt.data.Tag, decoded.Tag)
			}

			// 验证值是否相等（这里简化处理，实际应该根据类型进行详细比较）
			if decoded.Value == nil {
				t.Error("Decoded value should not be nil")
			}
		})
	}
}

func TestDataString(t *testing.T) {
	tests := []struct {
		name     string
		data     *Data
		contains string
	}{
		{"NULL", NewDataNull(), "Data[NULL]"},
		{"Bool", NewDataBool(true), "Data[bool]"},
		{"Integer", NewDataInteger(123), "Data[integer]"},
		{"String", NewDataVisibleString("test"), "Data[visible-string]"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			str := tt.data.String()
			if str == "" {
				t.Error("String() should not return empty string")
			}
			// 简单检查是否包含期望的类型名称
			// 实际的字符串格式可能会变化，这里只做基本验证
			t.Logf("Data string representation: %s", str)
		})
	}
}

func TestDataTypeName(t *testing.T) {
	tests := []struct {
		tag      byte
		expected string
	}{
		{DataTypeNull, "NULL"},
		{DataTypeBool, "bool"},
		{DataTypeInteger, "integer"},
		{DataTypeVisibleString, "visible-string"},
		{DataTypeOI, "OI"},
		{255, "Unknown(255)"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			data := &Data{Choice: &axdr.Choice{Tag: tt.tag}}
			typeName := data.GetTypeName()
			if typeName != tt.expected {
				t.Errorf("Expected type name '%s', got '%s'", tt.expected, typeName)
			}
		})
	}
}
