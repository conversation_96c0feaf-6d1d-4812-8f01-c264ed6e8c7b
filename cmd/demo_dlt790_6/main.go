package main

import (
	"fmt"
	"os"

	"tp.service/internal/protocol/dlt69845/datatype"
)

func main() {
	fmt.Println("=== DL/T 790.6-2010 标准内容演示 ===")
	fmt.Println()

	// 创建使用示例实例
	usage := datatype.NewDLT790_6_Usage()

	// 显示基本使用方法
	usage.ShowBasicUsage()

	// 打印统计信息
	usage.PrintStatistics()

	// 演示搜索功能
	demonstrateSearch(usage)

	// 演示页面内容获取
	demonstratePageContent(usage)

	// 演示编码规则查找
	demonstrateEncodingRules(usage)

	fmt.Println("=== 演示完成 ===")
}

func demonstrateSearch(usage *datatype.DLT790_6_Usage) {
	fmt.Println("=== 搜索功能演示 ===")

	searchTerms := []string{"A-XDR", "INTEGER", "编码", "字节串"}

	for _, term := range searchTerms {
		results := usage.SearchContent(term)
		fmt.Printf("搜索 '%s': 找到 %d 个结果\n", term, len(results))

		// 显示前3个结果
		for i, result := range results {
			if i >= 3 {
				break
			}
			fmt.Printf("  %s\n", result)
		}

		if len(results) > 3 {
			fmt.Printf("  ... 还有 %d 个结果\n", len(results)-3)
		}
		fmt.Println()
	}
}

func demonstratePageContent(usage *datatype.DLT790_6_Usage) {
	fmt.Println("=== 页面内容演示 ===")

	// 获取第1页内容
	page1 := usage.GetPageContent(1)
	fmt.Println("第1页内容预览:")
	if len(page1) > 300 {
		fmt.Printf("%s...\n", page1[:300])
	} else {
		fmt.Println(page1)
	}
	fmt.Println()

	// 获取第5页内容（编码规则相关）
	page5 := usage.GetPageContent(5)
	fmt.Println("第5页内容预览:")
	if len(page5) > 300 {
		fmt.Printf("%s...\n", page5[:300])
	} else {
		fmt.Println(page5)
	}
	fmt.Println()
}

func demonstrateEncodingRules(usage *datatype.DLT790_6_Usage) {
	fmt.Println("=== A-XDR 编码规则演示 ===")

	rules := usage.FindAXDRRules()
	fmt.Printf("找到 %d 个编码规则相关条目\n", len(rules))

	// 显示前10个结果
	for i, rule := range rules {
		if i >= 10 {
			break
		}
		fmt.Println(rule)
	}

	if len(rules) > 10 {
		fmt.Printf("... 还有 %d 个结果\n", len(rules)-10)
	}
	fmt.Println()
}

func init() {
	// 检查是否有命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "search":
			if len(os.Args) > 2 {
				runSearchDemo(os.Args[2])
				os.Exit(0)
			}
		case "page":
			if len(os.Args) > 2 {
				runPageDemo(os.Args[2])
				os.Exit(0)
			}
		case "help":
			showHelp()
			os.Exit(0)
		}
	}
}

func runSearchDemo(searchTerm string) {
	fmt.Printf("=== 搜索 '%s' ===\n", searchTerm)
	usage := datatype.NewDLT790_6_Usage()
	results := usage.SearchContent(searchTerm)

	fmt.Printf("找到 %d 个结果:\n", len(results))
	for i, result := range results {
		if i >= 20 { // 限制显示前20个结果
			fmt.Printf("... 还有 %d 个结果\n", len(results)-20)
			break
		}
		fmt.Println(result)
	}
}

func runPageDemo(pageStr string) {
	var pageNum int
	if _, err := fmt.Sscanf(pageStr, "%d", &pageNum); err != nil {
		fmt.Printf("无效的页码: %s\n", pageStr)
		return
	}

	fmt.Printf("=== 第 %d 页内容 ===\n", pageNum)
	usage := datatype.NewDLT790_6_Usage()
	content := usage.GetPageContent(pageNum)

	if len(content) == 0 {
		fmt.Printf("第 %d 页没有内容或页码不存在\n", pageNum)
		return
	}

	fmt.Println(content)
}

func showHelp() {
	fmt.Println("DL/T 790.6-2010 标准内容演示程序")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go                    # 运行完整演示")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go search <关键词>      # 搜索特定关键词")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go page <页码>         # 显示特定页面内容")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go help              # 显示帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go search A-XDR")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go search 编码")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go page 1")
	fmt.Println("  go run cmd/demo_dlt790_6/main.go page 5")
}
