package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/unidoc/unipdf/v3/extractor"
	"github.com/unidoc/unipdf/v3/model"
)

const (
	pdfPath    = "doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf"
	outputDir  = "internal/protocol/dlt69845/datatype"
	outputFile = "dlt790_6_standard.go"
)

type PDFSection struct {
	Title   string
	Content string
	Page    int
}

func main() {
	fmt.Println("开始使用高级方法提取 PDF 文档内容...")

	// 检查 PDF 文件是否存在
	if _, err := os.Stat(pdfPath); os.IsNotExist(err) {
		log.Fatalf("PDF 文件不存在: %s", pdfPath)
	}

	// 提取 PDF 内容
	sections, fullContent, err := extractPDFContentAdvanced(pdfPath)
	if err != nil {
		log.Fatalf("提取 PDF 内容失败: %v", err)
	}

	// 确保输出目录存在
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	// 生成 Go 文件
	if err := generateAdvancedGoFile(sections, fullContent); err != nil {
		log.Fatalf("生成 Go 文件失败: %v", err)
	}

	fmt.Printf("PDF 内容已成功提取并保存到: %s/%s\n", outputDir, outputFile)
	fmt.Printf("提取了 %d 个章节\n", len(sections))
}

func extractPDFContentAdvanced(filePath string) ([]PDFSection, string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, "", fmt.Errorf("打开 PDF 文件失败: %w", err)
	}
	defer file.Close()

	pdfReader, err := model.NewPdfReader(file)
	if err != nil {
		return nil, "", fmt.Errorf("创建 PDF 读取器失败: %w", err)
	}

	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return nil, "", fmt.Errorf("获取页数失败: %w", err)
	}

	fmt.Printf("PDF 总页数: %d\n", numPages)

	var sections []PDFSection
	var fullContent strings.Builder

	// 章节标题的正则表达式
	titleRegex := regexp.MustCompile(`^(\d+\.?\d*\.?\d*)\s+(.+)$`)

	for pageNum := 1; pageNum <= numPages; pageNum++ {
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			fmt.Printf("警告: 获取第 %d 页失败: %v\n", pageNum, err)
			continue
		}

		ex, err := extractor.New(page)
		if err != nil {
			fmt.Printf("警告: 创建第 %d 页提取器失败: %v\n", pageNum, err)
			continue
		}

		pageText, err := ex.ExtractText()
		if err != nil {
			fmt.Printf("警告: 提取第 %d 页文本失败: %v\n", pageNum, err)
			continue
		}

		// 清理文本
		cleanText := cleanText(pageText)
		
		fullContent.WriteString(fmt.Sprintf("// ===== 第 %d 页 =====\n", pageNum))
		fullContent.WriteString(cleanText)
		fullContent.WriteString("\n\n")

		// 尝试识别章节
		lines := strings.Split(cleanText, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if titleRegex.MatchString(line) {
				sections = append(sections, PDFSection{
					Title:   line,
					Content: cleanText,
					Page:    pageNum,
				})
			}
		}

		fmt.Printf("已提取第 %d 页内容\n", pageNum)
	}

	return sections, fullContent.String(), nil
}

func cleanText(text string) string {
	// 移除多余的空白字符
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	// 移除行首行尾空白
	text = strings.TrimSpace(text)
	// 处理换行
	text = strings.ReplaceAll(text, "\r\n", "\n")
	text = strings.ReplaceAll(text, "\r", "\n")
	
	return text
}

func generateAdvancedGoFile(sections []PDFSection, fullContent string) error {
	outputPath := filepath.Join(outputDir, outputFile)

	var goContent strings.Builder
	
	// 文件头
	goContent.WriteString(fmt.Sprintf(`// Code generated by advanced PDF extractor at %s
// Source: %s
// DO NOT EDIT manually

package datatype

import (
	"fmt"
	"strings"
)

// DLT790_6_Standard represents the DL/T 790.6-2010 standard content
// 采用配电线载波的配电自动化 第6部分: A-XDR编码规则 标准
type DLT790_6_Standard struct {
	FullContent string
	Sections    []Section
	ExtractedAt string
	SourceFile  string
}

// Section represents a section in the standard
type Section struct {
	Title   string
	Content string
	Page    int
}

`, time.Now().Format("2006-01-02 15:04:05"), pdfPath))

	// 添加完整内容常量
	goContent.WriteString("// DLT790_6_FullContent contains the complete extracted content\n")
	goContent.WriteString("const DLT790_6_FullContent = `")
	goContent.WriteString(strings.ReplaceAll(fullContent, "`", "` + \"`\" + `"))
	goContent.WriteString("`\n\n")

	// 添加章节数据
	goContent.WriteString("// DLT790_6_Sections contains all identified sections\n")
	goContent.WriteString("var DLT790_6_Sections = []Section{\n")
	for _, section := range sections {
		goContent.WriteString(fmt.Sprintf("\t{\n"))
		goContent.WriteString(fmt.Sprintf("\t\tTitle:   %q,\n", section.Title))
		goContent.WriteString(fmt.Sprintf("\t\tContent: %q,\n", section.Content))
		goContent.WriteString(fmt.Sprintf("\t\tPage:    %d,\n", section.Page))
		goContent.WriteString(fmt.Sprintf("\t},\n"))
	}
	goContent.WriteString("}\n\n")

	// 添加标准实例
	goContent.WriteString(fmt.Sprintf(`// DLT790_6 is the default instance of the standard
var DLT790_6 = &DLT790_6_Standard{
	FullContent: DLT790_6_FullContent,
	Sections:    DLT790_6_Sections,
	ExtractedAt: "%s",
	SourceFile:  "%s",
}

// GetFullContent returns the complete extracted content
func (s *DLT790_6_Standard) GetFullContent() string {
	return s.FullContent
}

// GetSections returns all sections
func (s *DLT790_6_Standard) GetSections() []Section {
	return s.Sections
}

// FindSection finds a section by title (case-insensitive partial match)
func (s *DLT790_6_Standard) FindSection(title string) *Section {
	title = strings.ToLower(title)
	for i, section := range s.Sections {
		if strings.Contains(strings.ToLower(section.Title), title) {
			return &s.Sections[i]
		}
	}
	return nil
}

// GetSectionsByPage returns all sections from a specific page
func (s *DLT790_6_Standard) GetSectionsByPage(page int) []Section {
	var result []Section
	for _, section := range s.Sections {
		if section.Page == page {
			result = append(result, section)
		}
	}
	return result
}

// String returns a formatted string representation
func (s *DLT790_6_Standard) String() string {
	return fmt.Sprintf("DL/T 790.6-2010 标准 (提取时间: %%s, 章节数: %%d)", 
		s.ExtractedAt, len(s.Sections))
}
`, time.Now().Format("2006-01-02 15:04:05"), pdfPath))

	// 写入文件
	if err := os.WriteFile(outputPath, []byte(goContent.String()), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}
