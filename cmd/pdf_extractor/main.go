package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/ledongthuc/pdf"
)

const (
	pdfPath    = "doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf"
	outputDir  = "internal/protocol/dlt69845/datatype"
	outputFile = "dlt790_6_extracted_content.go"
)

func main() {
	fmt.Println("开始提取 PDF 文档内容...")

	// 检查 PDF 文件是否存在
	if _, err := os.Stat(pdfPath); os.IsNotExist(err) {
		log.Fatalf("PDF 文件不存在: %s", pdfPath)
	}

	// 提取 PDF 内容
	content, err := extractPDFContent(pdfPath)
	if err != nil {
		log.Fatalf("提取 PDF 内容失败: %v", err)
	}

	// 确保输出目录存在
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	// 生成 Go 文件
	if err := generateGoFile(content); err != nil {
		log.Fatalf("生成 Go 文件失败: %v", err)
	}

	fmt.Printf("PDF 内容已成功提取并保存到: %s/%s\n", outputDir, outputFile)
}

func extractPDFContent(filePath string) (string, error) {
	file, reader, err := pdf.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开 PDF 文件失败: %w", err)
	}
	defer file.Close()

	var content strings.Builder
	totalPages := reader.NumPage()

	fmt.Printf("PDF 总页数: %d\n", totalPages)

	for pageNum := 1; pageNum <= totalPages; pageNum++ {
		page := reader.Page(pageNum)
		if page.V.IsNull() {
			continue
		}

		pageContent, err := page.GetPlainText(nil)
		if err != nil {
			fmt.Printf("警告: 提取第 %d 页内容失败: %v\n", pageNum, err)
			continue
		}

		content.WriteString(fmt.Sprintf("// ===== 第 %d 页 =====\n", pageNum))
		content.WriteString(pageContent)
		content.WriteString("\n\n")

		fmt.Printf("已提取第 %d 页内容\n", pageNum)
	}

	return content.String(), nil
}

func generateGoFile(content string) error {
	outputPath := filepath.Join(outputDir, outputFile)

	// 创建 Go 文件内容
	goContent := fmt.Sprintf(`// Code generated by PDF extractor at %s
// Source: %s
// DO NOT EDIT manually

package datatype

// DLT790_6_Content contains the extracted content from DL/T 790.6-2010 standard
// 采用配电线载波的配电自动化 第6部分: A-XDR编码规则 标准
const DLT790_6_Content = `+"`"+`%s`+"`"+`

// DLT790_6_Sections contains structured sections from the standard
var DLT790_6_Sections = map[string]string{
	"full_content": DLT790_6_Content,
	"extracted_at": "%s",
	"source_file":  "%s",
}

// GetDLT790_6_Content returns the full extracted content
func GetDLT790_6_Content() string {
	return DLT790_6_Content
}

// GetDLT790_6_Section returns a specific section if available
func GetDLT790_6_Section(section string) (string, bool) {
	content, exists := DLT790_6_Sections[section]
	return content, exists
}
`,
		time.Now().Format("2006-01-02 15:04:05"),
		pdfPath,
		strings.ReplaceAll(content, "`", "` + \"`\" + `"), // 转义反引号
		time.Now().Format("2006-01-02 15:04:05"),
		pdfPath,
	)

	// 写入文件
	if err := os.WriteFile(outputPath, []byte(goContent), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}
