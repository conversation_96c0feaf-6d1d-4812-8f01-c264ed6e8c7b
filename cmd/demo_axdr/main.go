// A-XDR 编解码演示程序
// 根据 DL/T 790.6-2010 标准实现
package main

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"tp.service/internal/protocol/dlt69845/axdr"
)

func main() {
	if len(os.Args) < 2 {
		showHelp()
		return
	}

	command := os.Args[1]

	switch command {
	case "examples":
		runExamples()
	case "test":
		runTests()
	case "encode":
		if len(os.Args) < 4 {
			fmt.Println("用法: go run cmd/demo_axdr/main.go encode <type> <value>")
			return
		}
		encodeValue(os.Args[2], os.Args[3])
	case "decode":
		if len(os.Args) < 4 {
			fmt.Println("用法: go run cmd/demo_axdr/main.go decode <type> <hex>")
			return
		}
		decodeValue(os.Args[2], os.Args[3])
	case "benchmark":
		runBenchmark()
	case "help":
		showHelp()
	default:
		fmt.Printf("未知命令: %s\n", command)
		showHelp()
	}
}

func showHelp() {
	fmt.Println("A-XDR 编解码演示程序")
	fmt.Println("基于 DL/T 790.6-2010 标准实现")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/demo_axdr/main.go <command> [args...]")
	fmt.Println()
	fmt.Println("命令:")
	fmt.Println("  examples              - 运行所有示例")
	fmt.Println("  test                  - 运行基础测试")
	fmt.Println("  encode <type> <value> - 编码指定类型的值")
	fmt.Println("  decode <type> <hex>   - 解码十六进制数据")
	fmt.Println("  benchmark             - 运行性能测试")
	fmt.Println("  help                  - 显示帮助信息")
	fmt.Println()
	fmt.Println("支持的类型:")
	fmt.Println("  integer    - 整数")
	fmt.Println("  boolean    - 布尔值 (true/false)")
	fmt.Println("  enum       - 枚举 (0-255)")
	fmt.Println("  string     - 可视字符串")
	fmt.Println("  bytes      - 字节串 (十六进制)")
	fmt.Println("  time       - 时间 (RFC3339格式)")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run cmd/demo_axdr/main.go examples")
	fmt.Println("  go run cmd/demo_axdr/main.go encode integer 12345")
	fmt.Println("  go run cmd/demo_axdr/main.go encode boolean true")
	fmt.Println("  go run cmd/demo_axdr/main.go encode string \"Hello\"")
	fmt.Println("  go run cmd/demo_axdr/main.go decode integer \"83303930\"")
}

func runExamples() {
	fmt.Println("运行 A-XDR 编解码示例...")
	fmt.Println()
	axdr.RunAllExamples()
}

func runTests() {
	fmt.Println("运行基础测试...")
	fmt.Println()

	codec := axdr.NewCodec()
	passed := 0
	total := 0

	// 测试 INTEGER
	total++
	fmt.Print("测试 INTEGER... ")
	integer := axdr.NewInteger(12345)
	encoded, err := codec.Encode(integer)
	if err != nil {
		fmt.Printf("失败: %v\n", err)
	} else {
		decoded := axdr.NewInteger(0)
		_, err = codec.Decode(encoded, decoded)
		if err != nil {
			fmt.Printf("失败: %v\n", err)
		} else if decoded.Value == 12345 {
			fmt.Println("通过")
			passed++
		} else {
			fmt.Printf("失败: 期望 12345, 得到 %d\n", decoded.Value)
		}
	}

	// 测试 BOOLEAN
	total++
	fmt.Print("测试 BOOLEAN... ")
	boolean := axdr.NewBoolean(true)
	encoded, err = codec.Encode(boolean)
	if err != nil {
		fmt.Printf("失败: %v\n", err)
	} else {
		decoded := axdr.NewBoolean(false)
		_, err = codec.Decode(encoded, decoded)
		if err != nil {
			fmt.Printf("失败: %v\n", err)
		} else if decoded.Value == true {
			fmt.Println("通过")
			passed++
		} else {
			fmt.Printf("失败: 期望 true, 得到 %t\n", decoded.Value)
		}
	}

	// 测试 VisibleString
	total++
	fmt.Print("测试 VisibleString... ")
	visibleString := axdr.NewVisibleString("Test")
	encoded, err = codec.Encode(visibleString)
	if err != nil {
		fmt.Printf("失败: %v\n", err)
	} else {
		decoded := axdr.NewVisibleString("")
		_, err = codec.Decode(encoded, decoded)
		if err != nil {
			fmt.Printf("失败: %v\n", err)
		} else if decoded.Value == "Test" {
			fmt.Println("通过")
			passed++
		} else {
			fmt.Printf("失败: 期望 \"Test\", 得到 \"%s\"\n", decoded.Value)
		}
	}

	fmt.Println()
	fmt.Printf("测试结果: %d/%d 通过\n", passed, total)
}

func encodeValue(typeStr, valueStr string) {
	codec := axdr.NewCodec()

	switch strings.ToLower(typeStr) {
	case "integer", "int":
		value, err := strconv.ParseInt(valueStr, 10, 64)
		if err != nil {
			fmt.Printf("无效的整数值: %s\n", valueStr)
			return
		}
		integer := axdr.NewInteger(value)
		integer.IsConstrained = true
		integer.MinValue = 0
		integer.MaxValue = 0xFFFF
		encoded, err := codec.Encode(integer)
		if err != nil {
			fmt.Printf("编码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: INTEGER\n")
		fmt.Printf("值: %d\n", value)
		fmt.Printf("编码: %s\n", axdr.FormatHex(encoded))

	case "boolean", "bool":
		value := strings.ToLower(valueStr) == "true"
		boolean := axdr.NewBoolean(value)
		encoded, err := codec.Encode(boolean)
		if err != nil {
			fmt.Printf("编码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: BOOLEAN\n")
		fmt.Printf("值: %t\n", value)
		fmt.Printf("编码: %s\n", axdr.FormatHex(encoded))

	case "enum", "enumerated":
		value, err := strconv.ParseUint(valueStr, 10, 8)
		if err != nil {
			fmt.Printf("无效的枚举值: %s (必须是 0-255)\n", valueStr)
			return
		}
		enumerated := axdr.NewEnumerated(byte(value))
		encoded, err := codec.Encode(enumerated)
		if err != nil {
			fmt.Printf("编码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: ENUMERATED\n")
		fmt.Printf("值: %d\n", value)
		fmt.Printf("编码: %s\n", axdr.FormatHex(encoded))

	case "string", "visiblestring":
		// 移除引号
		value := strings.Trim(valueStr, "\"'")
		visibleString := axdr.NewVisibleString(value)
		encoded, err := codec.Encode(visibleString)
		if err != nil {
			fmt.Printf("编码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: VisibleString\n")
		fmt.Printf("值: \"%s\"\n", value)
		fmt.Printf("编码: %s\n", axdr.FormatHex(encoded))

	case "bytes", "bytestring":
		data, err := axdr.ParseHex(valueStr)
		if err != nil {
			fmt.Printf("无效的十六进制数据: %s\n", valueStr)
			return
		}
		byteString := axdr.NewByteString(data)
		encoded, err := codec.Encode(byteString)
		if err != nil {
			fmt.Printf("编码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: BYTE STRING\n")
		fmt.Printf("值: %s\n", axdr.FormatHex(data))
		fmt.Printf("编码: %s\n", axdr.FormatHex(encoded))

	case "time", "generalizedtime":
		parsedTime, err := time.Parse(time.RFC3339, valueStr)
		if err != nil {
			fmt.Printf("无效的时间格式: %s (使用 RFC3339 格式)\n", valueStr)
			return
		}
		generalizedTime := axdr.NewGeneralizedTime(parsedTime)
		encoded, err := codec.Encode(generalizedTime)
		if err != nil {
			fmt.Printf("编码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: GeneralizedTime\n")
		fmt.Printf("值: %s\n", parsedTime.Format(time.RFC3339))
		fmt.Printf("编码: %s\n", axdr.FormatHex(encoded))

	default:
		fmt.Printf("不支持的类型: %s\n", typeStr)
		fmt.Println("支持的类型: integer, boolean, enum, string, bytes, time")
	}
}

func decodeValue(typeStr, hexStr string) {
	codec := axdr.NewCodec()

	data, err := axdr.ParseHex(hexStr)
	if err != nil {
		fmt.Printf("无效的十六进制数据: %s\n", hexStr)
		return
	}

	fmt.Printf("输入数据: %s\n", axdr.FormatHex(data))

	switch strings.ToLower(typeStr) {
	case "integer", "int":
		integer := axdr.NewInteger(0)
		consumed, err := codec.Decode(data, integer)
		if err != nil {
			fmt.Printf("解码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: INTEGER\n")
		fmt.Printf("值: %d\n", integer.Value)
		fmt.Printf("消耗字节: %d\n", consumed)

	case "boolean", "bool":
		boolean := axdr.NewBoolean(false)
		consumed, err := codec.Decode(data, boolean)
		if err != nil {
			fmt.Printf("解码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: BOOLEAN\n")
		fmt.Printf("值: %t\n", boolean.Value)
		fmt.Printf("消耗字节: %d\n", consumed)

	case "enum", "enumerated":
		enumerated := axdr.NewEnumerated(0)
		consumed, err := codec.Decode(data, enumerated)
		if err != nil {
			fmt.Printf("解码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: ENUMERATED\n")
		fmt.Printf("值: %d\n", enumerated.Value)
		fmt.Printf("消耗字节: %d\n", consumed)

	case "string", "visiblestring":
		visibleString := axdr.NewVisibleString("")
		consumed, err := codec.Decode(data, visibleString)
		if err != nil {
			fmt.Printf("解码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: VisibleString\n")
		fmt.Printf("值: \"%s\"\n", visibleString.Value)
		fmt.Printf("消耗字节: %d\n", consumed)

	case "bytes", "bytestring":
		byteString := axdr.NewByteString(nil)
		consumed, err := codec.Decode(data, byteString)
		if err != nil {
			fmt.Printf("解码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: BYTE STRING\n")
		fmt.Printf("值: %s\n", axdr.FormatHex(byteString.Value))
		fmt.Printf("消耗字节: %d\n", consumed)

	case "time", "generalizedtime":
		generalizedTime := axdr.NewGeneralizedTime(time.Time{})
		consumed, err := codec.Decode(data, generalizedTime)
		if err != nil {
			fmt.Printf("解码失败: %v\n", err)
			return
		}
		fmt.Printf("类型: GeneralizedTime\n")
		fmt.Printf("值: %s\n", generalizedTime.Value.Format(time.RFC3339))
		fmt.Printf("消耗字节: %d\n", consumed)

	default:
		fmt.Printf("不支持的类型: %s\n", typeStr)
		fmt.Println("支持的类型: integer, boolean, enum, string, bytes, time")
	}
}

func runBenchmark() {
	fmt.Println("运行性能测试...")
	fmt.Println()

	codec := axdr.NewCodec()
	iterations := 10000

	// INTEGER 性能测试
	start := time.Now()
	for i := 0; i < iterations; i++ {
		integer := axdr.NewInteger(int64(i))
		encoded, _ := codec.Encode(integer)
		decoded := axdr.NewInteger(0)
		codec.Decode(encoded, decoded)
	}
	duration := time.Since(start)
	fmt.Printf("INTEGER 编解码 %d 次: %v (平均 %.2f μs/次)\n",
		iterations, duration, float64(duration.Nanoseconds())/float64(iterations)/1000)

	// VisibleString 性能测试
	start = time.Now()
	testString := "Hello World Test String"
	for i := 0; i < iterations; i++ {
		visibleString := axdr.NewVisibleString(testString)
		encoded, _ := codec.Encode(visibleString)
		decoded := axdr.NewVisibleString("")
		codec.Decode(encoded, decoded)
	}
	duration = time.Since(start)
	fmt.Printf("VisibleString 编解码 %d 次: %v (平均 %.2f μs/次)\n",
		iterations, duration, float64(duration.Nanoseconds())/float64(iterations)/1000)

	fmt.Println()
	fmt.Println("性能测试完成")
}
